"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSignaturePurchase.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSignaturePurchase.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSignaturePurchase: () => (/* binding */ useSignaturePurchase)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/./node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/apiService */ \"(app-pages-browser)/./src/lib/apiService.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/../node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useSignaturePurchase auto */ \n\n\n\n// Contract configuration - in production this should come from environment variables\nconst CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0 // Replace with actual contract address\n;\nconst USDT_JETTON_MASTER_ADDRESS = 'kQAzft3exsq946eO92eOF0QkQqNFOLaPHak18Xdy4OYG9WjN' // Testnet USDT jetton master\n;\nconsole.log('Using auction contract address:', CONTRACT_ADDRESS);\nconsole.log('Using USDT jetton master address:', USDT_JETTON_MASTER_ADDRESS);\nfunction useSignaturePurchase() {\n    var _wallet_account;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isCalculating: false,\n        isProcessing: false,\n        calculation: null,\n        error: null\n    });\n    /**\n   * Calculate purchase with signature verification\n   * This is the only supported purchase method - all purchases must be signature verified\n   */ const calculatePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[calculatePurchase]\": async (params)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'Wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                        ...prev,\n                        isCalculating: true,\n                        error: null,\n                        calculation: null\n                    })\n            }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n            try {\n                // Convert amount to nanotons/smallest unit\n                const amountBigInt = BigInt(Math.floor(parseFloat(params.amount) * **********));\n                const currency = params.currency === 'TON' ? 0 : 1;\n                const response = await _lib_apiService__WEBPACK_IMPORTED_MODULE_3__.ApiService.calculatePurchase({\n                    user_address: wallet.account.address,\n                    amount: amountBigInt.toString(),\n                    currency\n                });\n                if (!response.success) {\n                    throw new Error(response.error || 'Calculation failed');\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            calculation: response\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return response;\n            } catch (error) {\n                const errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[calculatePurchase]\"], [\n        wallet\n    ]);\n    /**\n   * Execute purchase with signature verification\n   * All purchases must go through this signature verification process\n   */ const executePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executePurchase]\": async ()=>{\n            if (!wallet || !state.calculation || !tonConnectUI) {\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'No calculation available or wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: true,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n            try {\n                const calc = state.calculation.calculation;\n                const signature = state.calculation.signature;\n                console.log('Executing purchase with signature:', {\n                    calculation: calc,\n                    signature: signature\n                });\n                console.log('Contract address:', CONTRACT_ADDRESS);\n                console.log('Calculation details:', {\n                    user: calc.user,\n                    amount: calc.amount,\n                    currency: calc.currency,\n                    tokens_to_receive: calc.tokens_to_receive,\n                    current_price: calc.current_price,\n                    current_round: calc.current_round,\n                    timestamp: calc.timestamp,\n                    nonce: calc.nonce\n                });\n                // Convert signature from base64 to buffer\n                const signatureBuffer = Buffer.from(signature, 'base64');\n                // Create signature cell\n                const signatureCell = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeBuffer(signatureBuffer).endCell();\n                // Create the PurchaseWithSignature message body following the exact generated structure\n                const purchaseBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(1524770820, 32) // PurchaseWithSignature op code\n                // Store PurchaseCalculation inline (not as a separate cell)\n                .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user)).storeCoins(BigInt(calc.amount)).storeUint(calc.currency, 8).storeCoins(BigInt(calc.tokens_to_receive)).storeCoins(BigInt(calc.current_price)).storeUint(calc.current_round, 32).storeUint(calc.timestamp, 64).storeUint(BigInt(calc.nonce), 64)// Store signature as reference\n                .storeRef(signatureCell).endCell();\n                console.log('Message structure:', {\n                    opCode: '0x' + 1524770820..toString(16),\n                    signatureCellBits: signatureCell.bits.length,\n                    totalBodyBits: purchaseBody.bits.length,\n                    bodyBocLength: purchaseBody.toBoc().length\n                });\n                // Create transaction request\n                const transactionRequest = {\n                    validUntil: Math.floor(Date.now() / 1000) + 600,\n                    messages: [\n                        {\n                            address: CONTRACT_ADDRESS,\n                            amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                            payload: Buffer.from(purchaseBody.toBoc()).toString('base64')\n                        }\n                    ]\n                };\n                console.log('Transaction request:', {\n                    contractAddress: CONTRACT_ADDRESS,\n                    gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                    validUntil: transactionRequest.validUntil,\n                    payloadLength: transactionRequest.messages[0].payload.length\n                });\n                // Send transaction using TON Connect UI\n                const result = await tonConnectUI.sendTransaction(transactionRequest);\n                console.log('Transaction sent:', result);\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            isProcessing: false,\n                            calculation: null // Clear after successful purchase\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return true;\n            } catch (error) {\n                console.error('Transaction failed:', error);\n                let errorMessage = 'Transaction failed';\n                if (error instanceof Error) {\n                    if (error.message.includes('User rejected')) {\n                        errorMessage = 'Transaction was cancelled by user';\n                    } else if (error.message.includes('Insufficient funds')) {\n                        errorMessage = 'Insufficient funds for transaction';\n                    } else {\n                        errorMessage = error.message;\n                    }\n                } else {\n                    errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            isProcessing: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[executePurchase]\"], [\n        wallet,\n        state.calculation,\n        tonConnectUI\n    ]);\n    /**\n   * Clear current calculation\n   */ const clearCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearCalculation]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearCalculation]\": (prev)=>({\n                        ...prev,\n                        calculation: null,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearCalculation]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearCalculation]\"], []);\n    /**\n   * Clear error\n   */ const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearError]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearError]\": (prev)=>({\n                        ...prev,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearError]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearError]\"], []);\n    /**\n   * Get formatted calculation data for display\n   */ const getFormattedCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[getFormattedCalculation]\": ()=>{\n            if (!state.calculation) return null;\n            const calc = state.calculation.calculation;\n            return {\n                amount: parseFloat(calc.amount) / **********,\n                currency: calc.currency === 0 ? 'TON' : 'USDT',\n                tokensToReceive: parseFloat(calc.tokens_to_receive) / **********,\n                currentPrice: parseFloat(calc.current_price) / **********,\n                currentRound: calc.current_round,\n                timestamp: new Date(calc.timestamp * 1000),\n                nonce: calc.nonce\n            };\n        }\n    }[\"useSignaturePurchase.useCallback[getFormattedCalculation]\"], [\n        state.calculation\n    ]);\n    return {\n        ...state,\n        calculatePurchase,\n        executePurchase,\n        clearCalculation,\n        clearError,\n        getFormattedCalculation,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSignaturePurchase.ts\n"));

/***/ })

});