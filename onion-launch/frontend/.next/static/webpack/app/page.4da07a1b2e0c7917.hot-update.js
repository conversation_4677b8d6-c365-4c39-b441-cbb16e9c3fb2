"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSignaturePurchase.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSignaturePurchase.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSignaturePurchase: () => (/* binding */ useSignaturePurchase)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/./node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/apiService */ \"(app-pages-browser)/./src/lib/apiService.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/../node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useSignaturePurchase auto */ \n\n\n\n// Contract configuration - in production this should come from environment variables\nconst CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0 // Replace with actual contract address\n;\nconst USDT_JETTON_MASTER_ADDRESS = 'kQAzft3exsq946eO92eOF0QkQqNFOLaPHak18Xdy4OYG9WjN' // Testnet USDT jetton master\n;\nconsole.log('Using auction contract address:', CONTRACT_ADDRESS);\nconsole.log('Using USDT jetton master address:', USDT_JETTON_MASTER_ADDRESS);\n// Helper function to calculate jetton wallet address\nasync function getJettonWalletAddress(ownerAddress, jettonMasterAddress) {\n    try {\n        // Use TonWeb to calculate jetton wallet address\n        const TonWeb = (await Promise.resolve().then(function webpackMissingModule() { var e = new Error(\"Cannot find module 'tonweb'\"); e.code = 'MODULE_NOT_FOUND'; throw e; })).default;\n        const tonweb = new TonWeb(new TonWeb.HttpProvider('https://testnet.toncenter.com/api/v2/jsonRPC'));\n        //@ts-ignore\n        const jettonMasterContract = new TonWeb.token.jetton.JettonMinter(tonweb.provider, {\n            address: jettonMasterAddress\n        });\n        const jettonWalletAddress = await jettonMasterContract.getJettonWalletAddress(new TonWeb.utils.Address(ownerAddress));\n        return jettonWalletAddress.toString();\n    } catch (error) {\n        console.error('Failed to get jetton wallet address:', error);\n        throw new Error('Failed to calculate USDT wallet address');\n    }\n}\nfunction useSignaturePurchase() {\n    var _wallet_account;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isCalculating: false,\n        isProcessing: false,\n        calculation: null,\n        error: null\n    });\n    /**\n   * Calculate purchase with signature verification\n   * This is the only supported purchase method - all purchases must be signature verified\n   */ const calculatePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[calculatePurchase]\": async (params)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'Wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                        ...prev,\n                        isCalculating: true,\n                        error: null,\n                        calculation: null\n                    })\n            }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n            try {\n                // Convert amount to nanotons/smallest unit\n                const amountBigInt = BigInt(Math.floor(parseFloat(params.amount) * **********));\n                const currency = params.currency === 'TON' ? 0 : 1;\n                const response = await _lib_apiService__WEBPACK_IMPORTED_MODULE_3__.ApiService.calculatePurchase({\n                    user_address: wallet.account.address,\n                    amount: amountBigInt.toString(),\n                    currency\n                });\n                if (!response.success) {\n                    throw new Error(response.error || 'Calculation failed');\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            calculation: response\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return response;\n            } catch (error) {\n                const errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[calculatePurchase]\"], [\n        wallet\n    ]);\n    /**\n   * Execute purchase with signature verification\n   * All purchases must go through this signature verification process\n   */ const executePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executePurchase]\": async ()=>{\n            if (!wallet || !state.calculation || !tonConnectUI) {\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'No calculation available or wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: true,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n            try {\n                const calc = state.calculation.calculation;\n                const signature = state.calculation.signature;\n                console.log('Executing purchase with signature:', {\n                    calculation: calc,\n                    signature: signature\n                });\n                console.log('Contract address:', CONTRACT_ADDRESS);\n                console.log('Calculation details:', {\n                    user: calc.user,\n                    amount: calc.amount,\n                    currency: calc.currency,\n                    tokens_to_receive: calc.tokens_to_receive,\n                    current_price: calc.current_price,\n                    current_round: calc.current_round,\n                    timestamp: calc.timestamp,\n                    nonce: calc.nonce\n                });\n                // Convert signature from base64 to buffer\n                const signatureBuffer = Buffer.from(signature, 'base64');\n                // Create signature cell\n                const signatureCell = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeBuffer(signatureBuffer).endCell();\n                // Create the PurchaseWithSignature message body following the exact generated structure\n                const purchaseBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(1524770820, 32) // PurchaseWithSignature op code\n                // Store PurchaseCalculation inline (not as a separate cell)\n                .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user)).storeCoins(BigInt(calc.amount)).storeUint(calc.currency, 8).storeCoins(BigInt(calc.tokens_to_receive)).storeCoins(BigInt(calc.current_price)).storeUint(calc.current_round, 32).storeUint(calc.timestamp, 64).storeUint(BigInt(calc.nonce), 64)// Store signature as reference\n                .storeRef(signatureCell).endCell();\n                console.log('Message structure:', {\n                    opCode: '0x' + 1524770820..toString(16),\n                    signatureCellBits: signatureCell.bits.length,\n                    totalBodyBits: purchaseBody.bits.length,\n                    bodyBocLength: purchaseBody.toBoc().length\n                });\n                // Create transaction request\n                const transactionRequest = {\n                    validUntil: Math.floor(Date.now() / 1000) + 600,\n                    messages: [\n                        {\n                            address: CONTRACT_ADDRESS,\n                            amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                            payload: Buffer.from(purchaseBody.toBoc()).toString('base64')\n                        }\n                    ]\n                };\n                console.log('Transaction request:', {\n                    contractAddress: CONTRACT_ADDRESS,\n                    gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                    validUntil: transactionRequest.validUntil,\n                    payloadLength: transactionRequest.messages[0].payload.length\n                });\n                // Send transaction using TON Connect UI\n                const result = await tonConnectUI.sendTransaction(transactionRequest);\n                console.log('Transaction sent:', result);\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            isProcessing: false,\n                            calculation: null // Clear after successful purchase\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return true;\n            } catch (error) {\n                console.error('Transaction failed:', error);\n                let errorMessage = 'Transaction failed';\n                if (error instanceof Error) {\n                    if (error.message.includes('User rejected')) {\n                        errorMessage = 'Transaction was cancelled by user';\n                    } else if (error.message.includes('Insufficient funds')) {\n                        errorMessage = 'Insufficient funds for transaction';\n                    } else {\n                        errorMessage = error.message;\n                    }\n                } else {\n                    errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            isProcessing: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[executePurchase]\"], [\n        wallet,\n        state.calculation,\n        tonConnectUI\n    ]);\n    /**\n   * Clear current calculation\n   */ const clearCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearCalculation]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearCalculation]\": (prev)=>({\n                        ...prev,\n                        calculation: null,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearCalculation]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearCalculation]\"], []);\n    /**\n   * Clear error\n   */ const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearError]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearError]\": (prev)=>({\n                        ...prev,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearError]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearError]\"], []);\n    /**\n   * Get formatted calculation data for display\n   */ const getFormattedCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[getFormattedCalculation]\": ()=>{\n            if (!state.calculation) return null;\n            const calc = state.calculation.calculation;\n            return {\n                amount: parseFloat(calc.amount) / **********,\n                currency: calc.currency === 0 ? 'TON' : 'USDT',\n                tokensToReceive: parseFloat(calc.tokens_to_receive) / **********,\n                currentPrice: parseFloat(calc.current_price) / **********,\n                currentRound: calc.current_round,\n                timestamp: new Date(calc.timestamp * 1000),\n                nonce: calc.nonce\n            };\n        }\n    }[\"useSignaturePurchase.useCallback[getFormattedCalculation]\"], [\n        state.calculation\n    ]);\n    return {\n        ...state,\n        calculatePurchase,\n        executePurchase,\n        clearCalculation,\n        clearError,\n        getFormattedCalculation,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSignaturePurchase.ts\n"));

/***/ })

});