'use client'

import { useState } from 'react'
import { useTonWallet } from '@tonconnect/ui-react'
import { ShoppingCart, Calculator, AlertTriangle, CheckCircle, Zap, Clock, RefreshCw } from 'lucide-react'
import { useSignaturePurchase } from '@/hooks/useSignaturePurchase'
import { useWalletBalance } from '@/hooks/useWalletBalance'
import { useAuction } from '@/hooks/useAuction'
import { PurchaseConfirmModal } from './PurchaseConfirmModal'

export function PurchaseModule() {
  const wallet = useTonWallet()
  const [amount, setAmount] = useState('')
  const [currency, setCurrency] = useState<'TON' | 'USDT'>('TON')
  const [showConfirmModal, setShowConfirmModal] = useState(false)

  const {
    isCalculating,
    isProcessing,
    calculation,
    error,
    calculatePurchase,
    executePurchase,
    clearCalculation,
    clearError,
    getFormattedCalculation,
    isConnected
  } = useSignaturePurchase()

  const {
    balance,
    refreshBalance,
    hasInsufficientBalance,
    getBalance,
    formatBalance,
    isConnected: isWalletConnected
  } = useWalletBalance()

  const { auctionData } = useAuction()

  const currentPrice = 0.11 // USD per ONION (fallback)
  const tonPrice = 5.5 // USD per TON (mock price)
  const usdtPrice = 1.0 // USD per USDT

  const calculateTokens = () => {
    if (!amount) return 0

    // If we have a calculation from signature service, use that
    const formatted = getFormattedCalculation()
    if (formatted) {
      return formatted.tokensToReceive
    }

    // Fallback calculation
    const amountNum = parseFloat(amount)
    const amountInUSD = currency === 'TON' ? amountNum * tonPrice : amountNum * usdtPrice
    return amountInUSD / currentPrice
  }

  const isValidAmount = () => {
    if (!amount) return false
    const amountNum = parseFloat(amount)
    const minAmountUSD = 0.55 // 0.1 TON * 5.5 USD/TON = 0.55 USD
    const amountInUSD = currency === 'TON' ? amountNum * tonPrice : amountNum * usdtPrice
    return amountInUSD >= minAmountUSD
  }

  const getValidationErrors = () => {
    const errors: string[] = []

    if (!amount) return errors

    const amountNum = parseFloat(amount)

    // Check minimum amount
    const minAmountUSD = 0.55 // 0.1 TON * 5.5 USD/TON = 0.55 USD
    const amountInUSD = currency === 'TON' ? amountNum * tonPrice : amountNum * usdtPrice
    if (amountInUSD < minAmountUSD) {
      errors.push(`Amount must be at least $${minAmountUSD} USD equivalent`)
    }

    // Check wallet balance
    if (isWalletConnected && hasInsufficientBalance(amountNum, currency)) {
      const currentBalance = getBalance(currency)
      const needed = amountNum - currentBalance
      errors.push(`Insufficient ${currency} balance. You need ${needed.toFixed(6)} more ${currency}`)
    }

    // Check round token limit - different behavior for Round 1 vs Round 2+
    if (auctionData?.remainingTokensInRound) {
      const tokensToReceive = calculateTokens()
      const currentRound = auctionData.currentRound

      if (tokensToReceive > auctionData.remainingTokensInRound) {
        if (currentRound === 1) {
          // Round 1: Allow purchase, excess will be refunded
          // This is just a warning, not an error
        } else {
          // Round 2+: Show error, don't allow purchase
          errors.push(`Only ${auctionData.remainingTokensInRound.toLocaleString()} tokens available in current round`)
        }
      }
    }

    // Check total auction token limit
    if (auctionData) {
      const tokensToReceive = calculateTokens()
      const remainingTotal = auctionData.totalSupply - auctionData.totalTokensSold

      if (tokensToReceive > remainingTotal) {
        errors.push(`Only ${remainingTotal.toLocaleString()} tokens remaining in entire auction`)
      }
    }

    return errors
  }

  const handleCalculate = async () => {
    if (!isValidAmount()) return

    clearError()
    await calculatePurchase({ amount, currency })
  }

  const handlePurchase = async () => {
    if (!wallet || !isConnected) return

    // Ensure we have calculation first
    if (!calculation) {
      await handleCalculate()
      return
    }

    // Show confirmation modal
    setShowConfirmModal(true)
  }

  const handleConfirmPurchase = async () => {
    if (!wallet || !isConnected) return

    try {
      const success = await executePurchase()
      if (success) {
        setAmount('')
        setShowConfirmModal(false)
        // Refresh balance after successful purchase
        // refreshBalance()
      }
    } catch (error) {
      console.error('Purchase failed:', error)
      // Error handling is done in the hooks
    }
  }

  const tokensToReceive = calculateTokens()
  const minAmount = currency === 'TON' ? '0.1' : '0.1'
  const formatted = getFormattedCalculation()

  // Get validation errors
  const validationErrors = getValidationErrors()
  const hasErrors = validationErrors.length > 0

  // Prepare purchase details for confirmation modal
  const getPurchaseDetails = () => {
    const amountNum = parseFloat(amount) || 0
    const tokensToReceive = calculateTokens()
    const totalCostUSD = currency === 'TON' ? amountNum * tonPrice : amountNum * usdtPrice
    const formatted = getFormattedCalculation()

    return {
      amount,
      currency,
      tokensToReceive,
      currentPrice: formatted ? formatted.currentPrice : currentPrice,
      currentRound: formatted ? formatted.currentRound : (auctionData?.currentRound || 1),
      totalCostUSD,
      timestamp: formatted ? formatted.timestamp : new Date(),
      nonce: formatted?.nonce,
      isSignatureVerified: !!calculation
    }
  }

  return (
    <div className="bg-white rounded-2xl card-shadow p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="flex items-center justify-center w-10 h-10 bg-onion-100 rounded-lg">
          <ShoppingCart className="w-5 h-5 text-onion-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Purchase ONION</h2>
        <div className="flex items-center space-x-1 text-sm text-blue-600">
          <Zap className="w-4 h-4" />
          <span>Signature Verified</span>
        </div>
      </div>

      {/* Purchase Confirmation Modal */}
      <PurchaseConfirmModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleConfirmPurchase}
        purchaseDetails={getPurchaseDetails()}
        isProcessing={isProcessing}
        walletBalance={isWalletConnected ? { ton: balance.ton, usdt: balance.usdt } : undefined}
        remainingTokensInRound={auctionData?.remainingTokensInRound}
        currentRound={auctionData?.currentRound}
      />

      {!wallet ? (
        <div className="text-center py-8">
          <div className="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4 mx-auto">
            <AlertTriangle className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Connect Your Wallet</h3>
          <p className="text-gray-600 mb-4">
            Connect your TON wallet to participate in the fair launch
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Wallet Balance Display */}
          {isWalletConnected && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700">Wallet Balance</span>
                <button
                  onClick={refreshBalance}
                  disabled={balance.isLoading}
                  className="p-1 hover:bg-gray-200 rounded transition-colors disabled:opacity-50"
                  title="Refresh balance"
                >
                  <RefreshCw className={`w-4 h-4 text-gray-500 ${balance.isLoading ? 'animate-spin' : ''}`} />
                </button>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">
                    {balance.isLoading ? '...' : balance.ton.toFixed(2)}
                  </div>
                  <div className="text-xs text-gray-500">TON</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">
                    {balance.isLoading ? '...' : balance.usdt.toFixed(2)}
                  </div>
                  <div className="text-xs text-gray-500">USDT</div>
                </div>
              </div>
              {balance.error && (
                <div className="mt-2 text-xs text-red-600">
                  Failed to load balance: {balance.error}
                </div>
              )}
              {balance.lastUpdated && (
                <div className="mt-2 text-xs text-gray-500">
                  Last updated: {balance.lastUpdated.toLocaleTimeString()}
                </div>
              )}
            </div>
          )}

          {/* Current Price Display */}
          <div className="bg-gradient-to-r from-onion-50 to-onion-100 rounded-lg p-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-onion-700">Current Price</span>
              <span className="text-lg font-bold text-onion-800">
                ${formatted ? formatted.currentPrice.toFixed(3) : currentPrice.toFixed(3)}
              </span>
            </div>
            <div className="text-xs text-onion-600 mt-1">
              per ONION token • Round {formatted ? formatted.currentRound : 12}
            </div>
          </div>

          {/* Purchase Method Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Zap className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Signature Verified Purchase</span>
            </div>
            <div className="text-sm text-blue-700">
              All purchases use off-chain calculation with cryptographic signature verification for enhanced security and gas efficiency.
            </div>
          </div>

          {/* Currency Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => setCurrency('TON')}
                className={`p-3 rounded-lg border-2 transition-all ${
                  currency === 'TON'
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="font-medium">TON</div>
                <div className="text-xs text-gray-500">${tonPrice.toFixed(2)}</div>
              </button>
              <button
                onClick={() => setCurrency('USDT')}
                className={`p-3 rounded-lg border-2 transition-all ${
                  currency === 'USDT'
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="font-medium">USDT</div>
                <div className="text-xs text-gray-500">${usdtPrice.toFixed(2)}</div>
              </button>
            </div>
          </div>

          {/* Amount Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Amount ({currency})
            </label>
            <div className="relative">
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder={`Min: ${minAmount} ${currency}`}
                className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-onion-500 focus:border-transparent"
                step="0.01"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <span className="text-sm font-medium text-gray-500">{currency}</span>
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Minimum: {minAmount} {currency} (~$0.55 USD equivalent)
            </div>
          </div>

          {/* Calculate Button */}
          {amount && isValidAmount() && !calculation && (
            <button
              onClick={handleCalculate}
              disabled={isCalculating}
              className="w-full py-3 rounded-lg font-semibold transition-all bg-blue-500 text-white hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              {isCalculating ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Calculating...</span>
                </div>
              ) : (
                'Calculate Purchase'
              )}
            </button>
          )}

          {/* Calculation Display */}
          {amount && calculation && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Calculator className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">Verified Calculation</span>
                <div className="flex items-center space-x-1 text-xs text-green-600">
                  <CheckCircle className="w-3 h-3" />
                  <span>Signed</span>
                </div>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount in USD:</span>
                  <span className="font-medium">
                    ${formatted ?
                      (formatted.amount * (formatted.currency === 'TON' ? tonPrice : usdtPrice)).toFixed(2) :
                      ((parseFloat(amount) || 0) * (currency === 'TON' ? tonPrice : usdtPrice)).toFixed(2)
                    }
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Price per ONION:</span>
                  <span className="font-medium">
                    ${formatted ? formatted.currentPrice.toFixed(3) : currentPrice.toFixed(3)}
                  </span>
                </div>
                {formatted && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Round:</span>
                    <span className="font-medium">{formatted.currentRound}</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between">
                  <span className="text-gray-900 font-medium">You will receive:</span>
                  <span className="font-bold text-onion-600">
                    {tokensToReceive.toLocaleString(undefined, { maximumFractionDigits: 0 })} ONION
                  </span>
                </div>
                {formatted && (
                  <div className="text-xs text-gray-500 mt-2">
                    Calculated at: {formatted.timestamp.toLocaleTimeString()}
                    <span className="ml-2">• Nonce: {formatted.nonce}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Error Messages */}
          {error && (
            <div className="flex items-center space-x-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg">
              <AlertTriangle className="w-4 h-4" />
              <span>{error}</span>
              <button onClick={clearError} className="ml-auto text-red-400 hover:text-red-600">
                ×
              </button>
            </div>
          )}

          {/* Validation Messages */}
          {validationErrors.length > 0 && (
            <div className="space-y-2">
              {validationErrors.map((errorMsg, index) => (
                <div key={index} className="flex items-center space-x-2 text-red-600 text-sm">
                  <AlertTriangle className="w-4 h-4" />
                  <span>{errorMsg}</span>
                </div>
              ))}
            </div>
          )}

          {amount && !hasErrors && !error && (
            <div className="flex items-center space-x-2 text-green-600 text-sm">
              <CheckCircle className="w-4 h-4" />
              <span>Valid purchase amount</span>
            </div>
          )}

          {/* Clear Calculation Button */}
          {calculation && (
            <button
              onClick={clearCalculation}
              className="w-full py-2 rounded-lg font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 transition-all"
            >
              Clear Calculation
            </button>
          )}

          {/* Purchase Button */}
          <button
            onClick={handlePurchase}
            disabled={
              !isValidAmount() ||
              hasErrors ||
              isProcessing ||
              (!calculation && !isCalculating)
            }
            className={`w-full py-4 rounded-lg font-semibold transition-all ${
              !isValidAmount() || hasErrors || isProcessing || (!calculation && !isCalculating)
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-onion-500 to-onion-600 text-white hover:from-onion-600 hover:to-onion-700 transform hover:scale-[1.02]'
            }`}
          >
            {isProcessing ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Processing...</span>
              </div>
            ) : hasErrors ? (
              'Fix Errors to Continue'
            ) : (
              !calculation ? 'Calculate First' : 'Purchase ONION Tokens'
            )}
          </button>

          {/* Risk Warning */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-sm">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
              <span className="font-medium text-yellow-800">Important Notice</span>
            </div>
            <div className="text-yellow-700 space-y-1">
              <p>• Refunds available during auction with 5% fee</p>
              <p>• Tokens distributed after successful auction completion</p>
              <p>• Price may increase in future rounds</p>
              <p>• Signature verification ensures accurate pricing</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}