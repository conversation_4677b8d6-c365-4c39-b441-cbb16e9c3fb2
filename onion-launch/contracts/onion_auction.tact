import "@stdlib/ownable";
import "@stdlib/stoppable";
import "@stdlib/deploy";
import "./jetton/JettonWallet";
import "./user_purchase";

// Error codes for better debugging
const ERROR_AUCTION_NOT_ACTIVE: Int = 551001;
const ERROR_AUCTION_NOT_STARTED: Int = 551002;
const ERROR_AUCTION_ENDED: Int = 551003;
const ERROR_INVALID_CURRENCY: Int = 551004;
const ERROR_AMOUNT_BELOW_MINIMUM: Int = 551005;
const ERROR_INSUFFICIENT_TOKENS: Int = 551006;
const ERROR_USDT_NOT_CONFIGURED: Int = 551007;
const ERROR_INVALID_USDT_WALLET: Int = 551008;
const ERROR_SIGNING_KEY_NOT_SET: Int = 551009;
const ERROR_SIGNATURE_EXPIRED: Int = 551010;
const ERROR_FUTURE_TIMESTAMP: Int = 551011;
const ERROR_NONCE_ALREADY_USED: Int = 551012;
const ERROR_INVALID_SIGNATURE: Int = 551013;
const ERROR_CURRENCY_MISMATCH: Int = 551014;
const ERROR_AMOUNT_MISMATCH: Int = 551015;
const ERROR_USER_MISMATCH: Int = 551016;
const ERROR_UNAUTHORIZED_REFUND: Int = 551017;
const ERROR_MIN_PURCHASE_INVALID: Int = 551018;

// Op codes for messages
const OP_PURCHASE: Int = 0xeb9a8c4c; // 3954698588
const OP_START_AUCTION: Int = 0x408b8b11; // 1082453649
const OP_UPDATE_ROUND: Int = 0xaa8b8b89; // 2860888489
const OP_SET_USDT_ADDRESS: Int = 0xa1234afe; // 2704015070
const OP_PURCHASE_WITH_SIGNATURE: Int = 0x5ae22804; // 1524770820
const OP_SET_SIGNING_KEY: Int = 0xc5a5c5fe; // 3314411774
const OP_SET_MIN_PURCHASE: Int = 0xca1ca1c8; // 3391141464

// Messages with explicit op codes
message(OP_PURCHASE) Purchase {
    amount: Int as coins;
    currency: Int as uint8; // 0=TON, 1=USDT
}

message(OP_START_AUCTION) StartAuction {
    start_time: Int as uint64;
    end_time: Int as uint64;
    soft_cap: Int as coins;
    hard_cap: Int as coins;
    initial_price: Int as coins;
}

message(OP_UPDATE_ROUND) UpdateRound {
    new_price: Int as coins;
    round_number: Int as uint32;
}

message(OP_SET_USDT_ADDRESS) SetUSDTAddress {
    usdt_master: Address;
    usdt_wallet: Address; // The USDT wallet address for this contract
}

// New signature verification messages
message(OP_PURCHASE_WITH_SIGNATURE) PurchaseWithSignature {
    calculation: PurchaseCalculation;
    signature: Slice; // Server signature
}

message(OP_SET_SIGNING_KEY) SetSigningKey {
    public_key: Int as uint256; // Server's public key for signature verification
}

message(OP_SET_MIN_PURCHASE) SetMinPurchase {
    min_purchase: Int as coins; // New minimum purchase amount
}

// Structs
struct AuctionConfig {
    start_time: Int as uint64;
    end_time: Int as uint64;
    soft_cap: Int as coins;
    hard_cap: Int as coins;
    total_supply: Int as coins;
    refund_fee_percent: Int as uint8; // 5% = 5
}

struct USDTConfig {
    master_address: Address;
    wallet_address: Address?;
    decimals: Int as uint8; // USDT has 6 decimals
}

// New struct for signature verification
struct PurchaseCalculation {
    user: Address;
    amount: Int as coins;
    currency: Int as uint8; // 0=TON, 1=USDT
    tokens_to_receive: Int as coins;
    current_price: Int as coins;
    current_round: Int as uint32;
    timestamp: Int as uint64;
    nonce: Int as uint64; // Anti-replay protection
}

// Struct for parsed purchase data from forward_payload
struct ParsedPurchaseData {
    calculation: PurchaseCalculation;
    signature: Slice;
}

// Main Auction Contract
contract OnionAuction with Ownable, Stoppable, Deployable {
    
    // State variables
    owner: Address;
    stopped: Bool;

    auction_config: AuctionConfig;
    current_round: Int as uint32;
    current_price: Int as coins;
    total_raised: Int as coins;
    total_tokens_sold: Int as coins;
    auction_status: Int as uint8; // 0=pending, 1=active, 2=ended_success, 3=ended_failure

    // USDT configuration
    usdt_config: USDTConfig?;
    total_raised_usdt: Int as coins; // Total USDT raised (in USDT units)

    // Counters
    purchase_count: Int as uint32;

    // Signature verification
    signing_public_key: Int as uint256; // Server's public key for signature verification
    used_nonces: map<Int, Bool>; // Track used nonces to prevent replay attacks
    signature_timeout: Int as uint64; // Signature validity window in seconds

    // Configurable parameters
    min_purchase: Int as coins; // Minimum purchase amount (configurable)

    // Constants
    const ROUND_DURATION: Int = 3600; // 1 hour in seconds
    const PRICE_INCREMENT: Int = ton("0.01"); // 0.01 TON per round
    const SIGNATURE_TIMEOUT: Int = 300; // 5 minutes signature validity
    
    init(
        owner: Address,
        start_time: Int,
        end_time: Int,
        soft_cap: Int,
        hard_cap: Int,
        total_supply: Int
    ) {
        self.owner = owner;
        self.stopped = false;

        self.auction_config = AuctionConfig{
            start_time: start_time,
            end_time: end_time,
            soft_cap: soft_cap,
            hard_cap: hard_cap,
            total_supply: total_supply,
            refund_fee_percent: 5
        };

        self.current_round = 1;
        self.current_price = ton("0.1"); // Initial price 0.1 TON per token
        self.total_raised = 0;
        self.total_tokens_sold = 0;
        self.auction_status = 0; // pending
        self.purchase_count = 0;

        // Initialize USDT config as null
        self.usdt_config = null;
        self.total_raised_usdt = 0;

        // Initialize signature verification
        self.signing_public_key = 0; // Will be set by owner
        self.signature_timeout = self.SIGNATURE_TIMEOUT;

        // Initialize configurable parameters
        self.min_purchase = ton("0.1"); // Default minimum purchase amount
    }
    
    
    // Deploy message handler
    receive("Deploy") {
        // Handle deployment - no special logic needed for this contract
    }
    
    // Set USDT address (only owner)
    receive(msg: SetUSDTAddress) {
        self.requireOwner();
        self.usdt_config = USDTConfig{
            master_address: msg.usdt_master,
            wallet_address: msg.usdt_wallet,
            decimals: 6 // USDT has 6 decimals
        };
    }

    // Set signing public key (only owner)
    receive(msg: SetSigningKey) {
        self.requireOwner();
        self.signing_public_key = msg.public_key;
    }

    // Set minimum purchase amount (only owner)
    receive(msg: SetMinPurchase) {
        self.requireOwner();
        throwUnless(ERROR_MIN_PURCHASE_INVALID, msg.min_purchase > 0);
        self.min_purchase = msg.min_purchase;
    }

    // Start auction
    receive(msg: StartAuction) {
        self.requireOwner();
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 0);

        self.auction_config.start_time = msg.start_time;
        self.auction_config.end_time = msg.end_time;
        self.auction_config.soft_cap = msg.soft_cap;
        self.auction_config.hard_cap = msg.hard_cap;
        self.current_price = msg.initial_price;
        self.auction_status = 1; // active
    }
    
    // Purchase tokens with TON
    receive(msg: Purchase) {
        self.requireNotStopped();
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 1);
        throwUnless(ERROR_AUCTION_NOT_STARTED, now() >= self.auction_config.start_time);
        throwUnless(ERROR_AUCTION_ENDED, now() <= self.auction_config.end_time);
        throwUnless(ERROR_INVALID_CURRENCY, msg.currency == 0);
        throwUnless(ERROR_AMOUNT_BELOW_MINIMUM, msg.amount >= self.min_purchase);

        let current_time: Int = now();
        self.updateCurrentRound(current_time);

        // Calculate tokens to receive
        let tokens_to_receive: Int = (msg.amount * ton("1")) / self.current_price;

        throwUnless(ERROR_INSUFFICIENT_TOKENS, self.total_tokens_sold + tokens_to_receive <= self.auction_config.total_supply);

        // Update state
        self.total_raised += msg.amount;
        self.total_tokens_sold += tokens_to_receive;
        self.purchase_count += 1;

        // Check if hard cap reached
        if (self.total_raised >= self.auction_config.hard_cap) {
            self.auction_status = 2; // ended_success
        }

        // Create or update user purchase contract
        self.createOrUpdateUserPurchase(sender(), msg.amount, tokens_to_receive, 0, 0, 0); // Direct purchase, no nonce
    }

    // Handle USDT transfers via Jetton Transfer Notification
    receive(msg: JettonTransferNotification) {
        self.requireNotStopped();
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 1);
        throwUnless(ERROR_AUCTION_NOT_STARTED, now() >= self.auction_config.start_time);
        throwUnless(ERROR_AUCTION_ENDED, now() <= self.auction_config.end_time);

        // Verify this is from our USDT jetton wallet
        throwUnless(ERROR_USDT_NOT_CONFIGURED, self.usdt_config != null);
        let usdt_config: USDTConfig = self.usdt_config!!;

        // Get expected USDT wallet address for this contract
        let expected_wallet: Address = self.getJettonWalletAddress(usdt_config.master_address, myAddress());
        throwUnless(ERROR_INVALID_USDT_WALLET, sender() == expected_wallet);

        // Check if forward_payload contains signature verification data
        if (msg.forward_payload != null) {
            // Handle USDT purchase with signature verification
            self.handleUSDTWithSignature(msg);
        } else {
            // Handle direct USDT purchase (backward compatibility)
            self.handleDirectUSDTPurchase(msg);
        }
    }

    // Purchase with signature verification (new method)
    receive(msg: PurchaseWithSignature) {
        self.requireNotStopped();
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 1);
        throwUnless(ERROR_AUCTION_NOT_STARTED, now() >= self.auction_config.start_time);
        throwUnless(ERROR_AUCTION_ENDED, now() <= self.auction_config.end_time);
        throwUnless(ERROR_SIGNING_KEY_NOT_SET, self.signing_public_key != 0);

        let calc: PurchaseCalculation = msg.calculation;

        // Verify signature timestamp is within valid window
        let current_time: Int = now();
        throwUnless(ERROR_SIGNATURE_EXPIRED, current_time - calc.timestamp <= self.signature_timeout);
        throwUnless(ERROR_FUTURE_TIMESTAMP, calc.timestamp <= current_time);

        // Check nonce to prevent replay attacks
        throwUnless(ERROR_NONCE_ALREADY_USED, self.used_nonces.get(calc.nonce) == null);

        // Verify the signature
        let data_hash: Int = self.hashPurchaseCalculation(calc);
        throwUnless(ERROR_INVALID_SIGNATURE, checkSignature(data_hash, msg.signature, self.signing_public_key));

        // Mark nonce as used
        self.used_nonces.set(calc.nonce, true);

        // Verify calculation data matches sender
        throwUnless(ERROR_USER_MISMATCH, calc.user == sender());
        throwUnless(ERROR_AMOUNT_BELOW_MINIMUM, calc.amount >= self.min_purchase);
        throwUnless(ERROR_INSUFFICIENT_TOKENS, self.total_tokens_sold + calc.tokens_to_receive <= self.auction_config.total_supply);

        // Update state based on currency
        if (calc.currency == 0) {
            // TON purchase
            self.total_raised += calc.amount;
        } else if (calc.currency == 1) {
            // USDT purchase
            throwUnless(ERROR_USDT_NOT_CONFIGURED, self.usdt_config != null);
            self.total_raised_usdt += calc.amount;
        } else {
            throwUnless(ERROR_INVALID_CURRENCY, false);
        }

        self.total_tokens_sold += calc.tokens_to_receive;
        self.purchase_count += 1;

        // Check if hard cap reached
        let total_raised_equivalent: Int = self.total_raised + (self.total_raised_usdt * 1000);
        if (total_raised_equivalent >= self.auction_config.hard_cap) {
            self.auction_status = 2; // ended_success
        }

        // Create or update user purchase contract
        self.createOrUpdateUserPurchase(calc.user, calc.amount, calc.tokens_to_receive, calc.currency, 1, calc.nonce); // Signature verified purchase
    }

    // Process refund request
    receive(msg: ProcessRefund) {
        self.requireNotStopped();
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 1);
        throwUnless(ERROR_AUCTION_ENDED, now() <= self.auction_config.end_time);

        // Calculate expected user purchase contract address
        let expected_user_purchase_addr: Address = contractAddress(self.getUserPurchaseInit(msg.user));
        throwUnless(ERROR_UNAUTHORIZED_REFUND, expected_user_purchase_addr == sender());

        let fee: Int = (msg.amount * self.auction_config.refund_fee_percent) / 100;
        let refund_amount: Int = msg.amount - fee;

        if (msg.currency == 0) {
            // TON refund
            self.total_raised -= msg.amount;

            // Send TON refund to user
            send(SendParameters{
                to: msg.user,
                value: refund_amount,
                mode: SendIgnoreErrors,
                bounce: false,
                body: "TON refund processed".asComment()
            });
        } else if (msg.currency == 1) {
            // USDT refund
            throwUnless(ERROR_USDT_NOT_CONFIGURED, self.usdt_config != null);
            self.total_raised_usdt -= msg.amount;

            let usdt_config: USDTConfig = self.usdt_config!!;
            throwUnless(ERROR_USDT_NOT_CONFIGURED, usdt_config.wallet_address != null);

            // Send USDT refund via jetton transfer
            send(SendParameters{
                to: usdt_config.wallet_address!!,
                value: ton("0.1"), // Gas for jetton transfer
                mode: SendIgnoreErrors,
                bounce: false,
                body: JettonTransfer{
                    query_id: 0,
                    amount: refund_amount,
                    destination: msg.user,
                    response_destination: myAddress(),
                    custom_payload: null,
                    forward_ton_amount: 1, // 1 unit for notification
                    forward_payload: "USDT refund processed".asComment()
                }.toCell()
            });
        }
    }
    
    // End auction
    receive("end_auction") {
        throwUnless(ERROR_AUCTION_NOT_ACTIVE, now() > self.auction_config.end_time || self.total_raised >= self.auction_config.hard_cap);
        
        if (self.total_raised >= self.auction_config.soft_cap) {
            self.auction_status = 2; // ended_success
        } else {
            self.auction_status = 3; // ended_failure
        }
    }
    
    // Update current round based on time
    fun updateCurrentRound(current_time: Int) {
        let elapsed_time: Int = current_time - self.auction_config.start_time;
        let new_round: Int = (elapsed_time / self.ROUND_DURATION) + 1;
        
        if (new_round > self.current_round) {
            self.current_round = new_round;
            self.current_price += self.PRICE_INCREMENT * (new_round - self.current_round);
        }
    }
    
    // Create or update user purchase contract
    fun createOrUpdateUserPurchase(user: Address, amount: Int, tokens: Int, currency: Int, purchase_method: Int, nonce: Int) {
        // Calculate user purchase contract address deterministically
        let init_code: StateInit = self.getUserPurchaseInit(user);
        let user_purchase_addr: Address = contractAddress(init_code);

        // Always send message to the calculated address
        // If contract doesn't exist, it will be deployed automatically
        send(SendParameters{
            to: user_purchase_addr,
            value: ton("0.2"), // Gas for contract deployment/operation
            mode: SendIgnoreErrors,
            bounce: false,
            body: CreateUserPurchase{
                user: user,
                amount: amount,
                tokens: tokens,
                currency: currency,
                purchase_method: purchase_method,
                nonce: nonce
            }.toCell(),
            code: init_code.code,
            data: init_code.data
        });
    }

    // Handle USDT purchase with signature verification
    fun handleUSDTWithSignature(msg: JettonTransferNotification) {
        throwUnless(ERROR_SIGNING_KEY_NOT_SET, self.signing_public_key != 0);

        // Parse signature verification data from forward_payload
        let parsed_data: ParsedPurchaseData = self.parsePurchaseFromPayload(msg.forward_payload!!);
        let calc: PurchaseCalculation = parsed_data.calculation;
        let signature: Slice = parsed_data.signature;

        // Verify calculation data matches the transfer
        throwUnless(ERROR_CURRENCY_MISMATCH, calc.currency == 1);
        throwUnless(ERROR_AMOUNT_MISMATCH, calc.amount == msg.amount);
        throwUnless(ERROR_USER_MISMATCH, calc.user == msg.sender);

        // Verify signature timestamp and nonce
        let current_time: Int = now();
        throwUnless(ERROR_SIGNATURE_EXPIRED, current_time - calc.timestamp <= self.signature_timeout);
        throwUnless(ERROR_FUTURE_TIMESTAMP, calc.timestamp <= current_time);
        throwUnless(ERROR_NONCE_ALREADY_USED, self.used_nonces.get(calc.nonce) == null);

        // Verify the signature
        let data_hash: Int = self.hashPurchaseCalculation(calc);
        throwUnless(ERROR_INVALID_SIGNATURE, checkSignature(data_hash, signature, self.signing_public_key));

        // Mark nonce as used
        self.used_nonces.set(calc.nonce, true);

        // Use pre-calculated values from signature
        throwUnless(ERROR_INSUFFICIENT_TOKENS, self.total_tokens_sold + calc.tokens_to_receive <= self.auction_config.total_supply);

        // Update state
        self.total_raised_usdt += calc.amount;
        self.total_tokens_sold += calc.tokens_to_receive;
        self.purchase_count += 1;

        // Check if hard cap reached
        let total_raised_equivalent: Int = self.total_raised + (self.total_raised_usdt * 1000);
        if (total_raised_equivalent >= self.auction_config.hard_cap) {
            self.auction_status = 2; // ended_success
        }

        // Create user purchase with signature verification
        self.createOrUpdateUserPurchase(calc.user, calc.amount, calc.tokens_to_receive, 1, 1, calc.nonce);
    }

    // Handle direct USDT purchase (backward compatibility)
    fun handleDirectUSDTPurchase(msg: JettonTransferNotification) {
        // Convert USDT amount to TON equivalent for token calculation
        // USDT has 6 decimals, TON has 9 decimals
        let usdt_amount_in_ton_units: Int = msg.amount * 1000; // Convert 6 decimals to 9 decimals
        throwUnless(ERROR_AMOUNT_BELOW_MINIMUM, usdt_amount_in_ton_units >= self.min_purchase);

        let current_time: Int = now();
        self.updateCurrentRound(current_time);

        // Calculate tokens to receive (using same price as TON)
        let tokens_to_receive: Int = (usdt_amount_in_ton_units * ton("1")) / self.current_price;

        throwUnless(ERROR_INSUFFICIENT_TOKENS, self.total_tokens_sold + tokens_to_receive <= self.auction_config.total_supply);

        // Update state
        self.total_raised_usdt += msg.amount; // Store in USDT units
        self.total_tokens_sold += tokens_to_receive;
        self.purchase_count += 1;

        // Check if hard cap reached (convert USDT to TON for comparison)
        let total_raised_equivalent: Int = self.total_raised + (self.total_raised_usdt * 1000);
        if (total_raised_equivalent >= self.auction_config.hard_cap) {
            self.auction_status = 2; // ended_success
        }

        // Create or update user purchase contract
        self.createOrUpdateUserPurchase(msg.sender, msg.amount, tokens_to_receive, 1, 0, 0); // Direct USDT purchase, no nonce
    }

    // Parse purchase calculation and signature from forward_payload
    fun parsePurchaseFromPayload(payload: Cell): ParsedPurchaseData {
        let payload_slice: Slice = payload.beginParse();

        // Parse PurchaseCalculation
        let calc: PurchaseCalculation = PurchaseCalculation{
            user: payload_slice.loadAddress(),
            amount: payload_slice.loadCoins(),
            currency: payload_slice.loadUint(8),
            tokens_to_receive: payload_slice.loadCoins(),
            current_price: payload_slice.loadCoins(),
            current_round: payload_slice.loadUint(32),
            timestamp: payload_slice.loadUint(64),
            nonce: payload_slice.loadUint(64)
        };

        // Parse signature from reference
        let signature: Slice = payload_slice.loadRef().beginParse();

        return ParsedPurchaseData{
            calculation: calc,
            signature: signature
        };
    }

    // Hash purchase calculation for signature verification
    fun hashPurchaseCalculation(calc: PurchaseCalculation): Int {
        // Create a cell with all calculation data
        let data_cell: Cell = beginCell()
            .storeAddress(calc.user)
            .storeCoins(calc.amount)
            .storeUint(calc.currency, 8)
            .storeCoins(calc.tokens_to_receive)
            .storeCoins(calc.current_price)
            .storeUint(calc.current_round, 32)
            .storeUint(calc.timestamp, 64)
            .storeUint(calc.nonce, 64)
            .endCell();

        // Return the hash of the cell
        return data_cell.hash();
    }

    // Get user purchase contract init
    fun getUserPurchaseInit(user: Address): StateInit {
        return initOf UserPurchase(myAddress(), user);
    }

    // Get jetton wallet address for a given owner and jetton master
    // Note: This is a simplified calculation. In practice, you should call the jetton master's get_wallet_address method
    fun getJettonWalletAddress(jetton_master: Address, owner: Address): Address {
        // For now, we'll store the expected wallet address in the USDT config
        // This should be set when configuring USDT
        if (self.usdt_config != null) {
            let config: USDTConfig = self.usdt_config!!;
            if (config.wallet_address != null) {
                return config.wallet_address!!;
            }
        }

        // Fallback: return the jetton master address (this is not correct but prevents compilation errors)
        // In production, this should be properly implemented
        return jetton_master;
    }
    
    // Getters
    get fun auction_info(): AuctionConfig {
        return self.auction_config;
    }
    
    get fun current_round(): Int {
        return self.current_round;
    }
    
    get fun current_price(): Int {
        return self.current_price;
    }
    
    get fun total_raised(): Int {
        return self.total_raised;
    }
    
    get fun total_tokens_sold(): Int {
        return self.total_tokens_sold;
    }
    
    get fun auction_status(): Int {
        return self.auction_status;
    }
    
    get fun purchase_count(): Int {
        return self.purchase_count;
    }
    
    get fun user_purchase_address(user: Address): Address {
        return contractAddress(self.getUserPurchaseInit(user));
    }
    
    get fun remaining_tokens(): Int {
        return self.auction_config.total_supply - self.total_tokens_sold;
    }
    
    get fun is_auction_active(): Bool {
        let current_time: Int = now();
        return self.auction_status == 1 &&
               current_time >= self.auction_config.start_time &&
               current_time <= self.auction_config.end_time;
    }

    get fun usdt_config(): USDTConfig? {
        return self.usdt_config;
    }

    get fun total_raised_usdt(): Int {
        return self.total_raised_usdt;
    }

    get fun total_raised_equivalent(): Int {
        // Return total raised in TON equivalent
        return self.total_raised + (self.total_raised_usdt * 1000);
    }

    get fun is_usdt_enabled(): Bool {
        return self.usdt_config != null;
    }

    // New getters for signature verification
    get fun signing_public_key(): Int {
        return self.signing_public_key;
    }

    get fun signature_timeout(): Int {
        return self.signature_timeout;
    }

    get fun is_nonce_used(nonce: Int): Bool {
        return self.used_nonces.get(nonce) != null;
    }

    get fun is_signature_verification_enabled(): Bool {
        return self.signing_public_key != 0;
    }

    get fun min_purchase(): Int {
        return self.min_purchase;
    }
}
