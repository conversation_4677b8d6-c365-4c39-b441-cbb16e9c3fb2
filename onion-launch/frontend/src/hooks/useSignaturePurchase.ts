'use client'

import { useState, useCallback } from 'react'
import { useTonWallet, useTonConnectUI } from '@tonconnect/ui-react'
import { beginCell, toNano, Address, Cell } from '@ton/core'
import { ApiService, PurchaseCalculationResponse, handleApiError } from '@/lib/apiService'
import { buildUSDTForwardPayload } from '@/lib/usdtPurchase'

// Contract configuration - in production this should come from environment variables
const CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_AUCTION_CONTRACT_ADDRESS || 'EQC...' // Replace with actual contract address
const USDT_JETTON_MASTER_ADDRESS = 'kQAzft3exsq946eO92eOF0QkQqNFOLaPHak18Xdy4OYG9WjN' // Testnet USDT jetton master

console.log('Using auction contract address:', CONTRACT_ADDRESS)
console.log('Using USDT jetton master address:', USDT_JETTON_MASTER_ADDRESS)

export interface SignaturePurchaseState {
  isCalculating: boolean
  isProcessing: boolean
  calculation: PurchaseCalculationResponse | null
  error: string | null
}

export interface PurchaseParams {
  amount: string
  currency: 'TON' | 'USDT'
}

// Helper function to calculate jetton wallet address
async function getJettonWalletAddress(ownerAddress: string, jettonMasterAddress: string): Promise<string> {
  try {
    // Use TonWeb to calculate jetton wallet address
    const TonWeb = (await import('tonweb')).default
    const tonweb = new TonWeb(new TonWeb.HttpProvider('https://testnet.toncenter.com/api/v2/jsonRPC'))

    //@ts-ignore
    const jettonMasterContract = new TonWeb.token.jetton.JettonMinter(tonweb.provider, {
      address: jettonMasterAddress
    })

    const jettonWalletAddress = await jettonMasterContract.getJettonWalletAddress(
      new TonWeb.utils.Address(ownerAddress)
    )
    return jettonWalletAddress.toString()
  } catch (error) {
    console.error('Failed to get jetton wallet address:', error)
    throw new Error('Failed to calculate USDT wallet address')
  }
}

export function useSignaturePurchase() {
  const wallet = useTonWallet()
  const [tonConnectUI] = useTonConnectUI()
  const [state, setState] = useState<SignaturePurchaseState>({
    isCalculating: false,
    isProcessing: false,
    calculation: null,
    error: null
  })

  /**
   * Calculate purchase with signature verification
   * This is the only supported purchase method - all purchases must be signature verified
   */
  const calculatePurchase = useCallback(async (params: PurchaseParams) => {
    if (!wallet?.account?.address) {
      setState(prev => ({ ...prev, error: 'Wallet not connected' }))
      return null
    }

    setState(prev => ({ 
      ...prev, 
      isCalculating: true, 
      error: null,
      calculation: null 
    }))

    try {
      // Convert amount to nanotons/smallest unit
      const amountBigInt = BigInt(Math.floor(parseFloat(params.amount) * **********))
      const currency = params.currency === 'TON' ? 0 : 1

      const response = await ApiService.calculatePurchase({
        user_address: wallet.account.address,
        amount: amountBigInt.toString(),
        currency
      })

      if (!response.success) {
        throw new Error(response.error || 'Calculation failed')
      }

      setState(prev => ({ 
        ...prev, 
        isCalculating: false,
        calculation: response
      }))

      return response
    } catch (error) {
      const errorMessage = handleApiError(error)
      setState(prev => ({ 
        ...prev, 
        isCalculating: false,
        error: errorMessage
      }))
      return null
    }
  }, [wallet])

  /**
   * Execute purchase with signature verification
   * All purchases must go through this signature verification process
   */
  const executePurchase = useCallback(async () => {
    if (!wallet || !state.calculation || !tonConnectUI) {
      setState(prev => ({ ...prev, error: 'No calculation available or wallet not connected' }))
      return false
    }

    setState(prev => ({ ...prev, isProcessing: true, error: null }))

    try {
      const calc = state.calculation.calculation
      const signature = state.calculation.signature

      console.log('Executing purchase with signature:', {
        calculation: calc,
        signature: signature
      })

      console.log('Contract address:', CONTRACT_ADDRESS)
      console.log('Calculation details:', {
        user: calc.user,
        amount: calc.amount,
        currency: calc.currency,
        tokens_to_receive: calc.tokens_to_receive,
        current_price: calc.current_price,
        current_round: calc.current_round,
        timestamp: calc.timestamp,
        nonce: calc.nonce
      })

      // Handle different currencies
      if (calc.currency === 0) {
        // TON purchase - direct contract call
        return await executeTONPurchase(calc, signature)
      } else if (calc.currency === 1) {
        // USDT purchase - jetton transfer
        return await executeUSDTPurchase(calc, signature)
      } else {
        throw new Error('Unsupported currency')
      }
    } catch (error) {
      console.error('Transaction failed:', error)
      let errorMessage = 'Transaction failed'

      if (error instanceof Error) {
        if (error.message.includes('User rejected')) {
          errorMessage = 'Transaction was cancelled by user'
        } else if (error.message.includes('Insufficient funds')) {
          errorMessage = 'Insufficient funds for transaction'
        } else {
          errorMessage = error.message
        }
      } else {
        errorMessage = handleApiError(error)
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: errorMessage
      }))
      return false
    }
  }, [wallet, state.calculation, tonConnectUI])

  /**
   * Execute TON purchase with signature verification
   */
  const executeTONPurchase = useCallback(async (calc: any, signature: string) => {
    // Convert signature from base64 to buffer
    const signatureBuffer = Buffer.from(signature, 'base64')

    // Create signature cell
    const signatureCell = beginCell()
      .storeBuffer(signatureBuffer)
      .endCell()

    // Create the PurchaseWithSignature message body following the exact generated structure
    const purchaseBody = beginCell()
      .storeUint(1524770820, 32) // PurchaseWithSignature op code
      // Store PurchaseCalculation inline (not as a separate cell)
      .storeAddress(Address.parse(calc.user))
      .storeCoins(BigInt(calc.amount))
      .storeUint(calc.currency, 8)
      .storeCoins(BigInt(calc.tokens_to_receive))
      .storeCoins(BigInt(calc.current_price))
      .storeUint(calc.current_round, 32)
      .storeUint(calc.timestamp, 64)
      .storeUint(BigInt(calc.nonce), 64)
      // Store signature as reference
      .storeRef(signatureCell)
      .endCell()

    console.log('Message structure:', {
      opCode: '0x' + (1524770820).toString(16),
      signatureCellBits: signatureCell.bits.length,
      totalBodyBits: purchaseBody.bits.length,
      bodyBocLength: purchaseBody.toBoc().length
    })

    // Create transaction request
    const transactionRequest = {
      validUntil: Math.floor(Date.now() / 1000) + 600, // 10 minutes validity
      messages: [
        {
          address: CONTRACT_ADDRESS,
          amount: toNano('0.5').toString(), // Gas fee for transaction
          payload: Buffer.from(purchaseBody.toBoc()).toString('base64')
        }
      ]
    }

    console.log('Transaction request:', {
      contractAddress: CONTRACT_ADDRESS,
      gasAmount: toNano('0.5').toString(),
      validUntil: transactionRequest.validUntil,
      payloadLength: transactionRequest.messages[0].payload.length
    })

    // Send transaction using TON Connect UI
    const result = await tonConnectUI.sendTransaction(transactionRequest)
    console.log('TON Transaction sent:', result)

    setState(prev => ({
      ...prev,
      isProcessing: false,
      calculation: null // Clear after successful purchase
    }))

    return true
  }, [tonConnectUI])

  /**
   * Execute USDT purchase with signature verification
   */
  const executeUSDTPurchase = useCallback(async (calc: any, signature: string) => {
    if (!wallet?.account?.address) {
      throw new Error('Wallet not connected')
    }

    // Get user's USDT jetton wallet address
    const userUSDTWalletAddress = await getJettonWalletAddress(
      wallet.account.address,
      USDT_JETTON_MASTER_ADDRESS
    )

    console.log('User USDT wallet address:', userUSDTWalletAddress)

    // Build forward payload with signature verification data
    const purchaseCalc = {
      user: Address.parse(calc.user),
      amount: BigInt(calc.amount),
      currency: calc.currency,
      tokensToReceive: BigInt(calc.tokens_to_receive),
      currentPrice: BigInt(calc.current_price),
      currentRound: calc.current_round,
      timestamp: calc.timestamp,
      nonce: BigInt(calc.nonce)
    }
    const forwardPayload = buildUSDTForwardPayload(purchaseCalc, signature)

    // Create jetton transfer message
    const jettonTransferBody = beginCell()
      .storeUint(0xf8a7ea5, 32) // JettonTransfer op code
      .storeUint(0, 64) // query_id
      .storeCoins(BigInt(calc.amount)) // amount
      .storeAddress(Address.parse(CONTRACT_ADDRESS)) // destination (auction contract)
      .storeAddress(Address.parse(wallet.account.address)) // response_destination
      .storeMaybeRef(null) // custom_payload
      .storeCoins(toNano('0.05')) // forward_ton_amount (gas for signature verification)
      .storeMaybeRef(forwardPayload) // forward_payload with signature data
      .endCell()

    console.log('USDT Transfer details:', {
      userWallet: userUSDTWalletAddress,
      amount: calc.amount,
      destination: CONTRACT_ADDRESS,
      forwardPayloadSize: forwardPayload.toBoc().length
    })

    // Create transaction request for USDT transfer
    const transactionRequest = {
      validUntil: Math.floor(Date.now() / 1000) + 600, // 10 minutes validity
      messages: [
        {
          address: userUSDTWalletAddress,
          amount: toNano('0.1').toString(), // Gas fee for jetton transfer
          payload: Buffer.from(jettonTransferBody.toBoc()).toString('base64')
        }
      ]
    }

    console.log('USDT Transaction request:', {
      jettonWalletAddress: userUSDTWalletAddress,
      gasAmount: toNano('0.1').toString(),
      validUntil: transactionRequest.validUntil,
      payloadLength: transactionRequest.messages[0].payload.length
    })

    // Send transaction using TON Connect UI
    const result = await tonConnectUI.sendTransaction(transactionRequest)
    console.log('USDT Transaction sent:', result)

    setState(prev => ({
      ...prev,
      isProcessing: false,
      calculation: null // Clear after successful purchase
    }))

    return true
  }, [wallet, tonConnectUI])

  /**
   * Clear current calculation
   */
  const clearCalculation = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      calculation: null,
      error: null
    }))
  }, [])

  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  /**
   * Get formatted calculation data for display
   */
  const getFormattedCalculation = useCallback(() => {
    if (!state.calculation) return null

    const calc = state.calculation.calculation
    return {
      amount: parseFloat(calc.amount) / **********, // Convert from nanotons
      currency: calc.currency === 0 ? 'TON' : 'USDT',
      tokensToReceive: parseFloat(calc.tokens_to_receive) / **********,
      currentPrice: parseFloat(calc.current_price) / **********,
      currentRound: calc.current_round,
      timestamp: new Date(calc.timestamp * 1000),
      nonce: calc.nonce
    }
  }, [state.calculation])

  return {
    ...state,
    calculatePurchase,
    executePurchase,
    clearCalculation,
    clearError,
    getFormattedCalculation,
    isConnected: !!wallet?.account?.address
  }
}
