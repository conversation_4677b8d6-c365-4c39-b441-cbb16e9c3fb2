# 交易调试指南

## 问题分析

根据失败的交易 `cc62521cb9db13d24bae4499ad1f0807207797055af480f496ca2a432872dd2a`，我们需要系统性地检查和修复问题。

## 已完成的修复

### 1. ✅ 添加了显式 Op Codes
```tact
// 在 onion_auction.tact 中添加了明确的 op codes
const OP_PURCHASE_WITH_SIGNATURE: Int = 0x5ae22804; // 1524770820

message(OP_PURCHASE_WITH_SIGNATURE) PurchaseWithSignature {
    calculation: PurchaseCalculation;
    signature: Slice;
}
```

### 2. ✅ 将 require 改为 throwUnless
```tact
// 替换了所有 require 语句为 throwUnless 并添加了错误代码
const ERROR_AUCTION_NOT_ACTIVE: Int = 1001;
const ERROR_SIGNATURE_EXPIRED: Int = 1010;
const ERROR_INVALID_SIGNATURE: Int = 1013;

// 示例
throwUnless(ERROR_AUCTION_NOT_ACTIVE, self.auction_status == 1);
throwUnless(ERROR_INVALID_SIGNATURE, checkSignature(data_hash, msg.signature, self.signing_public_key));
```

### 3. ✅ 修复了消息结构
修正了前端的消息构造，确保与生成的 TypeScript 代码一致：
```typescript
const purchaseBody = beginCell()
  .storeUint(1524770820, 32) // Op code
  // PurchaseCalculation 内联存储
  .storeAddress(Address.parse(calc.user))
  .storeCoins(BigInt(calc.amount))
  .storeUint(calc.currency, 8)
  .storeCoins(BigInt(calc.tokens_to_receive))
  .storeCoins(BigInt(calc.current_price))
  .storeUint(calc.current_round, 32)
  .storeUint(calc.timestamp, 64)
  .storeUint(BigInt(calc.nonce), 64)
  // 签名作为引用存储
  .storeRef(signatureCell)
  .endCell()
```

## 需要检查的问题

### 1. 合约状态检查
在 TON Explorer 中检查合约状态：
- 合约地址: `kQCqsh_qjhmyG7dtg3cTGWB1IxUceqXqD06mM-mqaI_SRTAJ`
- 检查是否已部署
- 检查拍卖状态是否为 1 (active)
- 检查签名公钥是否已设置

### 2. 服务器签名验证
确保后端服务器：
- 正确生成签名
- 使用正确的私钥
- 签名数据结构与合约期望一致

### 3. 时间戳验证
- 签名时间戳必须在 5 分钟内
- 确保客户端和服务器时间同步

## 调试步骤

### 步骤 1: 重新编译合约
```bash
cd onion-launch
npx tact --config tact.config.json
```

### 步骤 2: 重新部署合约（如果需要）
如果合约结构有重大变化，可能需要重新部署。

### 步骤 3: 设置合约状态
确保合约已正确初始化：
1. 设置签名公钥
2. 启动拍卖
3. 设置 USDT 配置（如果需要）

### 步骤 4: 测试交易
使用更新后的前端代码重新测试交易。

## 错误代码参考

| 错误代码 | 含义 | 解决方案 |
|---------|------|----------|
| 1001 | 拍卖未激活 | 检查拍卖状态，确保已启动 |
| 1002 | 拍卖未开始 | 检查开始时间 |
| 1003 | 拍卖已结束 | 检查结束时间 |
| 1009 | 签名密钥未设置 | 设置合约的签名公钥 |
| 1010 | 签名已过期 | 重新获取签名 |
| 1013 | 签名无效 | 检查签名生成逻辑 |

## 下一步行动

1. **重新编译合约**：使用更新的 Tact 代码
2. **检查合约状态**：确保所有必要的设置都已完成
3. **测试新的消息结构**：使用修复后的前端代码
4. **监控错误代码**：使用新的错误代码系统进行调试

## 预期结果

修复后，交易应该能够成功执行，或者至少能够提供更清晰的错误信息来帮助进一步调试。
