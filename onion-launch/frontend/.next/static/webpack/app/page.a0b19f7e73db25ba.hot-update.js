"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSignaturePurchase.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSignaturePurchase.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSignaturePurchase: () => (/* binding */ useSignaturePurchase)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/./node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/apiService */ \"(app-pages-browser)/./src/lib/apiService.ts\");\n/* harmony import */ var _lib_usdtPurchase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/usdtPurchase */ \"(app-pages-browser)/./src/lib/usdtPurchase.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/../node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useSignaturePurchase auto */ \n\n\n\n\n// Contract configuration - in production this should come from environment variables\nconst CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0 // Replace with actual contract address\n;\nconst USDT_JETTON_MASTER_ADDRESS = 'kQAzft3exsq946eO92eOF0QkQqNFOLaPHak18Xdy4OYG9WjN' // Testnet USDT jetton master\n;\nconsole.log('Using auction contract address:', CONTRACT_ADDRESS);\nconsole.log('Using USDT jetton master address:', USDT_JETTON_MASTER_ADDRESS);\n// Helper function to calculate jetton wallet address\nasync function getJettonWalletAddress(ownerAddress, jettonMasterAddress) {\n    try {\n        // Use TonWeb to calculate jetton wallet address\n        const TonWeb = (await Promise.resolve().then(function webpackMissingModule() { var e = new Error(\"Cannot find module 'tonweb'\"); e.code = 'MODULE_NOT_FOUND'; throw e; })).default;\n        const tonweb = new TonWeb(new TonWeb.HttpProvider('https://testnet.toncenter.com/api/v2/jsonRPC'));\n        //@ts-ignore\n        const jettonMasterContract = new TonWeb.token.jetton.JettonMinter(tonweb.provider, {\n            address: jettonMasterAddress\n        });\n        const jettonWalletAddress = await jettonMasterContract.getJettonWalletAddress(new TonWeb.utils.Address(ownerAddress));\n        return jettonWalletAddress.toString();\n    } catch (error) {\n        console.error('Failed to get jetton wallet address:', error);\n        throw new Error('Failed to calculate USDT wallet address');\n    }\n}\nfunction useSignaturePurchase() {\n    var _wallet_account;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isCalculating: false,\n        isProcessing: false,\n        calculation: null,\n        error: null\n    });\n    /**\n   * Calculate purchase with signature verification\n   * This is the only supported purchase method - all purchases must be signature verified\n   */ const calculatePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[calculatePurchase]\": async (params)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'Wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                        ...prev,\n                        isCalculating: true,\n                        error: null,\n                        calculation: null\n                    })\n            }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n            try {\n                // Convert amount to nanotons/smallest unit\n                const amountBigInt = BigInt(Math.floor(parseFloat(params.amount) * **********));\n                const currency = params.currency === 'TON' ? 0 : 1;\n                const response = await _lib_apiService__WEBPACK_IMPORTED_MODULE_3__.ApiService.calculatePurchase({\n                    user_address: wallet.account.address,\n                    amount: amountBigInt.toString(),\n                    currency\n                });\n                if (!response.success) {\n                    throw new Error(response.error || 'Calculation failed');\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            calculation: response\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return response;\n            } catch (error) {\n                const errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[calculatePurchase]\"], [\n        wallet\n    ]);\n    /**\n   * Execute purchase with signature verification\n   * All purchases must go through this signature verification process\n   */ const executePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executePurchase]\": async ()=>{\n            if (!wallet || !state.calculation || !tonConnectUI) {\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'No calculation available or wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: true,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n            try {\n                const calc = state.calculation.calculation;\n                const signature = state.calculation.signature;\n                console.log('Executing purchase with signature:', {\n                    calculation: calc,\n                    signature: signature\n                });\n                console.log('Contract address:', CONTRACT_ADDRESS);\n                console.log('Calculation details:', {\n                    user: calc.user,\n                    amount: calc.amount,\n                    currency: calc.currency,\n                    tokens_to_receive: calc.tokens_to_receive,\n                    current_price: calc.current_price,\n                    current_round: calc.current_round,\n                    timestamp: calc.timestamp,\n                    nonce: calc.nonce\n                });\n                // Handle different currencies\n                if (calc.currency === 0) {\n                    // TON purchase - direct contract call\n                    return await executeTONPurchase(calc, signature);\n                } else if (calc.currency === 1) {\n                    // USDT purchase - jetton transfer\n                    return await executeUSDTPurchase(calc, signature);\n                } else {\n                    throw new Error('Unsupported currency');\n                }\n            } catch (error) {\n                console.error('Transaction failed:', error);\n                let errorMessage = 'Transaction failed';\n                if (error instanceof Error) {\n                    if (error.message.includes('User rejected')) {\n                        errorMessage = 'Transaction was cancelled by user';\n                    } else if (error.message.includes('Insufficient funds')) {\n                        errorMessage = 'Insufficient funds for transaction';\n                    } else {\n                        errorMessage = error.message;\n                    }\n                } else {\n                    errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            isProcessing: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[executePurchase]\"], [\n        wallet,\n        state.calculation,\n        tonConnectUI\n    ]);\n    /**\n   * Execute TON purchase with signature verification\n   */ const executeTONPurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executeTONPurchase]\": async (calc, signature)=>{\n            // Convert signature from base64 to buffer\n            const signatureBuffer = Buffer.from(signature, 'base64');\n            // Create signature cell\n            const signatureCell = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeBuffer(signatureBuffer).endCell();\n            // Create the PurchaseWithSignature message body following the exact generated structure\n            const purchaseBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(1524770820, 32) // PurchaseWithSignature op code\n            // Store PurchaseCalculation inline (not as a separate cell)\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user)).storeCoins(BigInt(calc.amount)).storeUint(calc.currency, 8).storeCoins(BigInt(calc.tokens_to_receive)).storeCoins(BigInt(calc.current_price)).storeUint(calc.current_round, 32).storeUint(calc.timestamp, 64).storeUint(BigInt(calc.nonce), 64)// Store signature as reference\n            .storeRef(signatureCell).endCell();\n            console.log('Message structure:', {\n                opCode: '0x' + 1524770820..toString(16),\n                signatureCellBits: signatureCell.bits.length,\n                totalBodyBits: purchaseBody.bits.length,\n                bodyBocLength: purchaseBody.toBoc().length\n            });\n            // Create transaction request\n            const transactionRequest = {\n                validUntil: Math.floor(Date.now() / 1000) + 600,\n                messages: [\n                    {\n                        address: CONTRACT_ADDRESS,\n                        amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                        payload: Buffer.from(purchaseBody.toBoc()).toString('base64')\n                    }\n                ]\n            };\n            console.log('Transaction request:', {\n                contractAddress: CONTRACT_ADDRESS,\n                gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                validUntil: transactionRequest.validUntil,\n                payloadLength: transactionRequest.messages[0].payload.length\n            });\n            // Send transaction using TON Connect UI\n            const result = await tonConnectUI.sendTransaction(transactionRequest);\n            console.log('TON Transaction sent:', result);\n            setState({\n                \"useSignaturePurchase.useCallback[executeTONPurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: false,\n                        calculation: null // Clear after successful purchase\n                    })\n            }[\"useSignaturePurchase.useCallback[executeTONPurchase]\"]);\n            return true;\n        }\n    }[\"useSignaturePurchase.useCallback[executeTONPurchase]\"], [\n        tonConnectUI\n    ]);\n    /**\n   * Execute USDT purchase with signature verification\n   */ const executeUSDTPurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executeUSDTPurchase]\": async (calc, signature)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                throw new Error('Wallet not connected');\n            }\n            // Get user's USDT jetton wallet address\n            const userUSDTWalletAddress = await getJettonWalletAddress(wallet.account.address, USDT_JETTON_MASTER_ADDRESS);\n            console.log('User USDT wallet address:', userUSDTWalletAddress);\n            // Build forward payload with signature verification data\n            const purchaseCalc = {\n                user: _ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user),\n                amount: BigInt(calc.amount),\n                currency: calc.currency,\n                tokensToReceive: BigInt(calc.tokens_to_receive),\n                currentPrice: BigInt(calc.current_price),\n                currentRound: calc.current_round,\n                timestamp: calc.timestamp,\n                nonce: BigInt(calc.nonce)\n            };\n            const forwardPayload = (0,_lib_usdtPurchase__WEBPACK_IMPORTED_MODULE_4__.buildUSDTForwardPayload)(purchaseCalc, signature);\n            // Create jetton transfer message\n            const jettonTransferBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(0xf8a7ea5, 32) // JettonTransfer op code\n            .storeUint(0, 64) // query_id\n            .storeCoins(BigInt(calc.amount)) // amount\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(CONTRACT_ADDRESS)) // destination (auction contract)\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(wallet.account.address)) // response_destination\n            .storeMaybeRef(null) // custom_payload\n            .storeCoins((0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.05')) // forward_ton_amount (gas for signature verification)\n            .storeMaybeRef(forwardPayload) // forward_payload with signature data\n            .endCell();\n            console.log('USDT Transfer details:', {\n                userWallet: userUSDTWalletAddress,\n                amount: calc.amount,\n                destination: CONTRACT_ADDRESS,\n                forwardPayloadSize: forwardPayload.toBoc().length\n            });\n            // Create transaction request for USDT transfer\n            const transactionRequest = {\n                validUntil: Math.floor(Date.now() / 1000) + 600,\n                messages: [\n                    {\n                        address: userUSDTWalletAddress,\n                        amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.1').toString(),\n                        payload: Buffer.from(jettonTransferBody.toBoc()).toString('base64')\n                    }\n                ]\n            };\n            console.log('USDT Transaction request:', {\n                jettonWalletAddress: userUSDTWalletAddress,\n                gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.1').toString(),\n                validUntil: transactionRequest.validUntil,\n                payloadLength: transactionRequest.messages[0].payload.length\n            });\n            // Send transaction using TON Connect UI\n            const result = await tonConnectUI.sendTransaction(transactionRequest);\n            console.log('USDT Transaction sent:', result);\n            setState({\n                \"useSignaturePurchase.useCallback[executeUSDTPurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: false,\n                        calculation: null // Clear after successful purchase\n                    })\n            }[\"useSignaturePurchase.useCallback[executeUSDTPurchase]\"]);\n            return true;\n        }\n    }[\"useSignaturePurchase.useCallback[executeUSDTPurchase]\"], [\n        wallet,\n        tonConnectUI\n    ]);\n    /**\n   * Clear current calculation\n   */ const clearCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearCalculation]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearCalculation]\": (prev)=>({\n                        ...prev,\n                        calculation: null,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearCalculation]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearCalculation]\"], []);\n    /**\n   * Clear error\n   */ const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearError]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearError]\": (prev)=>({\n                        ...prev,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearError]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearError]\"], []);\n    /**\n   * Get formatted calculation data for display\n   */ const getFormattedCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[getFormattedCalculation]\": ()=>{\n            if (!state.calculation) return null;\n            const calc = state.calculation.calculation;\n            return {\n                amount: parseFloat(calc.amount) / **********,\n                currency: calc.currency === 0 ? 'TON' : 'USDT',\n                tokensToReceive: parseFloat(calc.tokens_to_receive) / **********,\n                currentPrice: parseFloat(calc.current_price) / **********,\n                currentRound: calc.current_round,\n                timestamp: new Date(calc.timestamp * 1000),\n                nonce: calc.nonce\n            };\n        }\n    }[\"useSignaturePurchase.useCallback[getFormattedCalculation]\"], [\n        state.calculation\n    ]);\n    return {\n        ...state,\n        calculatePurchase,\n        executePurchase,\n        clearCalculation,\n        clearError,\n        getFormattedCalculation,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VTaWduYXR1cmVQdXJjaGFzZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7MEVBRTZDO0FBQ3VCO0FBQ1I7QUFDOEI7QUFDOUI7QUFFNUQscUZBQXFGO0FBQ3JGLE1BQU1VLG1CQUFtQkMsa0RBQWdELElBQUksQ0FBUSxDQUFDLHVDQUF1Qzs7QUFDN0gsTUFBTUcsNkJBQTZCLG1EQUFtRCw2QkFBNkI7O0FBRW5IQyxRQUFRQyxHQUFHLENBQUMsbUNBQW1DTjtBQUMvQ0ssUUFBUUMsR0FBRyxDQUFDLHFDQUFxQ0Y7QUFjakQscURBQXFEO0FBQ3JELGVBQWVHLHVCQUF1QkMsWUFBb0IsRUFBRUMsbUJBQTJCO0lBQ3JGLElBQUk7UUFDRixnREFBZ0Q7UUFDaEQsTUFBTUMsU0FBUyxDQUFDLE1BQU0sbUpBQWUsRUFBR0MsT0FBTztRQUMvQyxNQUFNQyxTQUFTLElBQUlGLE9BQU8sSUFBSUEsT0FBT0csWUFBWSxDQUFDO1FBRWxELFlBQVk7UUFDWixNQUFNQyx1QkFBdUIsSUFBSUosT0FBT0ssS0FBSyxDQUFDQyxNQUFNLENBQUNDLFlBQVksQ0FBQ0wsT0FBT00sUUFBUSxFQUFFO1lBQ2pGQyxTQUFTVjtRQUNYO1FBRUEsTUFBTVcsc0JBQXNCLE1BQU1OLHFCQUFxQlAsc0JBQXNCLENBQzNFLElBQUlHLE9BQU9XLEtBQUssQ0FBQ3pCLE9BQU8sQ0FBQ1k7UUFFM0IsT0FBT1ksb0JBQW9CRSxRQUFRO0lBQ3JDLEVBQUUsT0FBT0MsT0FBTztRQUNkbEIsUUFBUWtCLEtBQUssQ0FBQyx3Q0FBd0NBO1FBQ3RELE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtBQUNGO0FBRU8sU0FBU0M7UUE4VEdDO0lBN1RqQixNQUFNQSxTQUFTbEMsa0VBQVlBO0lBQzNCLE1BQU0sQ0FBQ21DLGFBQWEsR0FBR2xDLHFFQUFlQTtJQUN0QyxNQUFNLENBQUNtQyxPQUFPQyxTQUFTLEdBQUd2QywrQ0FBUUEsQ0FBeUI7UUFDekR3QyxlQUFlO1FBQ2ZDLGNBQWM7UUFDZEMsYUFBYTtRQUNiVCxPQUFPO0lBQ1Q7SUFFQTs7O0dBR0MsR0FDRCxNQUFNVSxvQkFBb0IxQyxrREFBV0E7K0RBQUMsT0FBTzJDO2dCQUN0Q1I7WUFBTCxJQUFJLEVBQUNBLG1CQUFBQSw4QkFBQUEsa0JBQUFBLE9BQVFTLE9BQU8sY0FBZlQsc0NBQUFBLGdCQUFpQlAsT0FBTyxHQUFFO2dCQUM3QlU7MkVBQVNPLENBQUFBLE9BQVM7NEJBQUUsR0FBR0EsSUFBSTs0QkFBRWIsT0FBTzt3QkFBdUI7O2dCQUMzRCxPQUFPO1lBQ1Q7WUFFQU07dUVBQVNPLENBQUFBLE9BQVM7d0JBQ2hCLEdBQUdBLElBQUk7d0JBQ1BOLGVBQWU7d0JBQ2ZQLE9BQU87d0JBQ1BTLGFBQWE7b0JBQ2Y7O1lBRUEsSUFBSTtnQkFDRiwyQ0FBMkM7Z0JBQzNDLE1BQU1LLGVBQWVDLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ0MsV0FBV1AsT0FBT1EsTUFBTSxJQUFJO2dCQUNuRSxNQUFNQyxXQUFXVCxPQUFPUyxRQUFRLEtBQUssUUFBUSxJQUFJO2dCQUVqRCxNQUFNQyxXQUFXLE1BQU0vQyx1REFBVUEsQ0FBQ29DLGlCQUFpQixDQUFDO29CQUNsRFksY0FBY25CLE9BQU9TLE9BQU8sQ0FBQ2hCLE9BQU87b0JBQ3BDdUIsUUFBUUwsYUFBYWYsUUFBUTtvQkFDN0JxQjtnQkFDRjtnQkFFQSxJQUFJLENBQUNDLFNBQVNFLE9BQU8sRUFBRTtvQkFDckIsTUFBTSxJQUFJdEIsTUFBTW9CLFNBQVNyQixLQUFLLElBQUk7Z0JBQ3BDO2dCQUVBTTsyRUFBU08sQ0FBQUEsT0FBUzs0QkFDaEIsR0FBR0EsSUFBSTs0QkFDUE4sZUFBZTs0QkFDZkUsYUFBYVk7d0JBQ2Y7O2dCQUVBLE9BQU9BO1lBQ1QsRUFBRSxPQUFPckIsT0FBTztnQkFDZCxNQUFNd0IsZUFBZWpELCtEQUFjQSxDQUFDeUI7Z0JBQ3BDTTsyRUFBU08sQ0FBQUEsT0FBUzs0QkFDaEIsR0FBR0EsSUFBSTs0QkFDUE4sZUFBZTs0QkFDZlAsT0FBT3dCO3dCQUNUOztnQkFDQSxPQUFPO1lBQ1Q7UUFDRjs4REFBRztRQUFDckI7S0FBTztJQUVYOzs7R0FHQyxHQUNELE1BQU1zQixrQkFBa0J6RCxrREFBV0E7NkRBQUM7WUFDbEMsSUFBSSxDQUFDbUMsVUFBVSxDQUFDRSxNQUFNSSxXQUFXLElBQUksQ0FBQ0wsY0FBYztnQkFDbERFO3lFQUFTTyxDQUFBQSxPQUFTOzRCQUFFLEdBQUdBLElBQUk7NEJBQUViLE9BQU87d0JBQW1EOztnQkFDdkYsT0FBTztZQUNUO1lBRUFNO3FFQUFTTyxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUVMLGNBQWM7d0JBQU1SLE9BQU87b0JBQUs7O1lBRTdELElBQUk7Z0JBQ0YsTUFBTTBCLE9BQU9yQixNQUFNSSxXQUFXLENBQUNBLFdBQVc7Z0JBQzFDLE1BQU1rQixZQUFZdEIsTUFBTUksV0FBVyxDQUFDa0IsU0FBUztnQkFFN0M3QyxRQUFRQyxHQUFHLENBQUMsc0NBQXNDO29CQUNoRDBCLGFBQWFpQjtvQkFDYkMsV0FBV0E7Z0JBQ2I7Z0JBRUE3QyxRQUFRQyxHQUFHLENBQUMscUJBQXFCTjtnQkFDakNLLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0I7b0JBQ2xDNkMsTUFBTUYsS0FBS0UsSUFBSTtvQkFDZlQsUUFBUU8sS0FBS1AsTUFBTTtvQkFDbkJDLFVBQVVNLEtBQUtOLFFBQVE7b0JBQ3ZCUyxtQkFBbUJILEtBQUtHLGlCQUFpQjtvQkFDekNDLGVBQWVKLEtBQUtJLGFBQWE7b0JBQ2pDQyxlQUFlTCxLQUFLSyxhQUFhO29CQUNqQ0MsV0FBV04sS0FBS00sU0FBUztvQkFDekJDLE9BQU9QLEtBQUtPLEtBQUs7Z0JBQ25CO2dCQUVBLDhCQUE4QjtnQkFDOUIsSUFBSVAsS0FBS04sUUFBUSxLQUFLLEdBQUc7b0JBQ3ZCLHNDQUFzQztvQkFDdEMsT0FBTyxNQUFNYyxtQkFBbUJSLE1BQU1DO2dCQUN4QyxPQUFPLElBQUlELEtBQUtOLFFBQVEsS0FBSyxHQUFHO29CQUM5QixrQ0FBa0M7b0JBQ2xDLE9BQU8sTUFBTWUsb0JBQW9CVCxNQUFNQztnQkFDekMsT0FBTztvQkFDTCxNQUFNLElBQUkxQixNQUFNO2dCQUNsQjtZQUNGLEVBQUUsT0FBT0QsT0FBTztnQkFDZGxCLFFBQVFrQixLQUFLLENBQUMsdUJBQXVCQTtnQkFDckMsSUFBSXdCLGVBQWU7Z0JBRW5CLElBQUl4QixpQkFBaUJDLE9BQU87b0JBQzFCLElBQUlELE1BQU1vQyxPQUFPLENBQUNDLFFBQVEsQ0FBQyxrQkFBa0I7d0JBQzNDYixlQUFlO29CQUNqQixPQUFPLElBQUl4QixNQUFNb0MsT0FBTyxDQUFDQyxRQUFRLENBQUMsdUJBQXVCO3dCQUN2RGIsZUFBZTtvQkFDakIsT0FBTzt3QkFDTEEsZUFBZXhCLE1BQU1vQyxPQUFPO29CQUM5QjtnQkFDRixPQUFPO29CQUNMWixlQUFlakQsK0RBQWNBLENBQUN5QjtnQkFDaEM7Z0JBRUFNO3lFQUFTTyxDQUFBQSxPQUFTOzRCQUNoQixHQUFHQSxJQUFJOzRCQUNQTCxjQUFjOzRCQUNkUixPQUFPd0I7d0JBQ1Q7O2dCQUNBLE9BQU87WUFDVDtRQUNGOzREQUFHO1FBQUNyQjtRQUFRRSxNQUFNSSxXQUFXO1FBQUVMO0tBQWE7SUFFNUM7O0dBRUMsR0FDRCxNQUFNOEIscUJBQXFCbEUsa0RBQVdBO2dFQUFDLE9BQU8wRCxNQUFXQztZQUN2RCwwQ0FBMEM7WUFDMUMsTUFBTVcsa0JBQWtCQyxNQUFNQSxDQUFDQyxJQUFJLENBQUNiLFdBQVc7WUFFL0Msd0JBQXdCO1lBQ3hCLE1BQU1jLGdCQUFnQnRFLG9EQUFTQSxHQUM1QnVFLFdBQVcsQ0FBQ0osaUJBQ1pLLE9BQU87WUFFVix3RkFBd0Y7WUFDeEYsTUFBTUMsZUFBZXpFLG9EQUFTQSxHQUMzQjBFLFNBQVMsQ0FBQyxZQUFZLElBQUksZ0NBQWdDO1lBQzNELDREQUE0RDthQUMzREMsWUFBWSxDQUFDekUsOENBQU9BLENBQUMwRSxLQUFLLENBQUNyQixLQUFLRSxJQUFJLEdBQ3BDb0IsVUFBVSxDQUFDakMsT0FBT1csS0FBS1AsTUFBTSxHQUM3QjBCLFNBQVMsQ0FBQ25CLEtBQUtOLFFBQVEsRUFBRSxHQUN6QjRCLFVBQVUsQ0FBQ2pDLE9BQU9XLEtBQUtHLGlCQUFpQixHQUN4Q21CLFVBQVUsQ0FBQ2pDLE9BQU9XLEtBQUtJLGFBQWEsR0FDcENlLFNBQVMsQ0FBQ25CLEtBQUtLLGFBQWEsRUFBRSxJQUM5QmMsU0FBUyxDQUFDbkIsS0FBS00sU0FBUyxFQUFFLElBQzFCYSxTQUFTLENBQUM5QixPQUFPVyxLQUFLTyxLQUFLLEdBQUcsR0FDL0IsK0JBQStCO2FBQzlCZ0IsUUFBUSxDQUFDUixlQUNURSxPQUFPO1lBRVY3RCxRQUFRQyxHQUFHLENBQUMsc0JBQXNCO2dCQUNoQ21FLFFBQVEsT0FBTyxZQUFhbkQsUUFBUSxDQUFDO2dCQUNyQ29ELG1CQUFtQlYsY0FBY1csSUFBSSxDQUFDQyxNQUFNO2dCQUM1Q0MsZUFBZVYsYUFBYVEsSUFBSSxDQUFDQyxNQUFNO2dCQUN2Q0UsZUFBZVgsYUFBYVksS0FBSyxHQUFHSCxNQUFNO1lBQzVDO1lBRUEsNkJBQTZCO1lBQzdCLE1BQU1JLHFCQUFxQjtnQkFDekJDLFlBQVkxQyxLQUFLQyxLQUFLLENBQUMwQyxLQUFLQyxHQUFHLEtBQUssUUFBUTtnQkFDNUNDLFVBQVU7b0JBQ1I7d0JBQ0VqRSxTQUFTbkI7d0JBQ1QwQyxRQUFRL0MsaURBQU1BLENBQUMsT0FBTzJCLFFBQVE7d0JBQzlCK0QsU0FBU3ZCLE1BQU1BLENBQUNDLElBQUksQ0FBQ0ksYUFBYVksS0FBSyxJQUFJekQsUUFBUSxDQUFDO29CQUN0RDtpQkFDRDtZQUNIO1lBRUFqQixRQUFRQyxHQUFHLENBQUMsd0JBQXdCO2dCQUNsQ2dGLGlCQUFpQnRGO2dCQUNqQnVGLFdBQVc1RixpREFBTUEsQ0FBQyxPQUFPMkIsUUFBUTtnQkFDakMyRCxZQUFZRCxtQkFBbUJDLFVBQVU7Z0JBQ3pDTyxlQUFlUixtQkFBbUJJLFFBQVEsQ0FBQyxFQUFFLENBQUNDLE9BQU8sQ0FBQ1QsTUFBTTtZQUM5RDtZQUVBLHdDQUF3QztZQUN4QyxNQUFNYSxTQUFTLE1BQU05RCxhQUFhK0QsZUFBZSxDQUFDVjtZQUNsRDNFLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJtRjtZQUVyQzVEO3dFQUFTTyxDQUFBQSxPQUFTO3dCQUNoQixHQUFHQSxJQUFJO3dCQUNQTCxjQUFjO3dCQUNkQyxhQUFhLEtBQUssa0NBQWtDO29CQUN0RDs7WUFFQSxPQUFPO1FBQ1Q7K0RBQUc7UUFBQ0w7S0FBYTtJQUVqQjs7R0FFQyxHQUNELE1BQU0rQixzQkFBc0JuRSxrREFBV0E7aUVBQUMsT0FBTzBELE1BQVdDO2dCQUNuRHhCO1lBQUwsSUFBSSxFQUFDQSxtQkFBQUEsOEJBQUFBLGtCQUFBQSxPQUFRUyxPQUFPLGNBQWZULHNDQUFBQSxnQkFBaUJQLE9BQU8sR0FBRTtnQkFDN0IsTUFBTSxJQUFJSyxNQUFNO1lBQ2xCO1lBRUEsd0NBQXdDO1lBQ3hDLE1BQU1tRSx3QkFBd0IsTUFBTXBGLHVCQUNsQ21CLE9BQU9TLE9BQU8sQ0FBQ2hCLE9BQU8sRUFDdEJmO1lBR0ZDLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJxRjtZQUV6Qyx5REFBeUQ7WUFDekQsTUFBTUMsZUFBZTtnQkFDbkJ6QyxNQUFNdkQsOENBQU9BLENBQUMwRSxLQUFLLENBQUNyQixLQUFLRSxJQUFJO2dCQUM3QlQsUUFBUUosT0FBT1csS0FBS1AsTUFBTTtnQkFDMUJDLFVBQVVNLEtBQUtOLFFBQVE7Z0JBQ3ZCa0QsaUJBQWlCdkQsT0FBT1csS0FBS0csaUJBQWlCO2dCQUM5QzBDLGNBQWN4RCxPQUFPVyxLQUFLSSxhQUFhO2dCQUN2QzBDLGNBQWM5QyxLQUFLSyxhQUFhO2dCQUNoQ0MsV0FBV04sS0FBS00sU0FBUztnQkFDekJDLE9BQU9sQixPQUFPVyxLQUFLTyxLQUFLO1lBQzFCO1lBQ0EsTUFBTXdDLGlCQUFpQmpHLDBFQUF1QkEsQ0FBQzZGLGNBQWMxQztZQUU3RCxpQ0FBaUM7WUFDakMsTUFBTStDLHFCQUFxQnZHLG9EQUFTQSxHQUNqQzBFLFNBQVMsQ0FBQyxXQUFXLElBQUkseUJBQXlCO2FBQ2xEQSxTQUFTLENBQUMsR0FBRyxJQUFJLFdBQVc7YUFDNUJHLFVBQVUsQ0FBQ2pDLE9BQU9XLEtBQUtQLE1BQU0sR0FBRyxTQUFTO2FBQ3pDMkIsWUFBWSxDQUFDekUsOENBQU9BLENBQUMwRSxLQUFLLENBQUN0RSxtQkFBbUIsaUNBQWlDO2FBQy9FcUUsWUFBWSxDQUFDekUsOENBQU9BLENBQUMwRSxLQUFLLENBQUM1QyxPQUFPUyxPQUFPLENBQUNoQixPQUFPLEdBQUcsdUJBQXVCO2FBQzNFK0UsYUFBYSxDQUFDLE1BQU0saUJBQWlCO2FBQ3JDM0IsVUFBVSxDQUFDNUUsaURBQU1BLENBQUMsU0FBUyxzREFBc0Q7YUFDakZ1RyxhQUFhLENBQUNGLGdCQUFnQixzQ0FBc0M7YUFDcEU5QixPQUFPO1lBRVY3RCxRQUFRQyxHQUFHLENBQUMsMEJBQTBCO2dCQUNwQzZGLFlBQVlSO2dCQUNaakQsUUFBUU8sS0FBS1AsTUFBTTtnQkFDbkIwRCxhQUFhcEc7Z0JBQ2JxRyxvQkFBb0JMLGVBQWVqQixLQUFLLEdBQUdILE1BQU07WUFDbkQ7WUFFQSwrQ0FBK0M7WUFDL0MsTUFBTUkscUJBQXFCO2dCQUN6QkMsWUFBWTFDLEtBQUtDLEtBQUssQ0FBQzBDLEtBQUtDLEdBQUcsS0FBSyxRQUFRO2dCQUM1Q0MsVUFBVTtvQkFDUjt3QkFDRWpFLFNBQVN3RTt3QkFDVGpELFFBQVEvQyxpREFBTUEsQ0FBQyxPQUFPMkIsUUFBUTt3QkFDOUIrRCxTQUFTdkIsTUFBTUEsQ0FBQ0MsSUFBSSxDQUFDa0MsbUJBQW1CbEIsS0FBSyxJQUFJekQsUUFBUSxDQUFDO29CQUM1RDtpQkFDRDtZQUNIO1lBRUFqQixRQUFRQyxHQUFHLENBQUMsNkJBQTZCO2dCQUN2Q2MscUJBQXFCdUU7Z0JBQ3JCSixXQUFXNUYsaURBQU1BLENBQUMsT0FBTzJCLFFBQVE7Z0JBQ2pDMkQsWUFBWUQsbUJBQW1CQyxVQUFVO2dCQUN6Q08sZUFBZVIsbUJBQW1CSSxRQUFRLENBQUMsRUFBRSxDQUFDQyxPQUFPLENBQUNULE1BQU07WUFDOUQ7WUFFQSx3Q0FBd0M7WUFDeEMsTUFBTWEsU0FBUyxNQUFNOUQsYUFBYStELGVBQWUsQ0FBQ1Y7WUFDbEQzRSxRQUFRQyxHQUFHLENBQUMsMEJBQTBCbUY7WUFFdEM1RDt5RUFBU08sQ0FBQUEsT0FBUzt3QkFDaEIsR0FBR0EsSUFBSTt3QkFDUEwsY0FBYzt3QkFDZEMsYUFBYSxLQUFLLGtDQUFrQztvQkFDdEQ7O1lBRUEsT0FBTztRQUNUO2dFQUFHO1FBQUNOO1FBQVFDO0tBQWE7SUFFekI7O0dBRUMsR0FDRCxNQUFNMkUsbUJBQW1CL0csa0RBQVdBOzhEQUFDO1lBQ25Dc0M7c0VBQVNPLENBQUFBLE9BQVM7d0JBQ2hCLEdBQUdBLElBQUk7d0JBQ1BKLGFBQWE7d0JBQ2JULE9BQU87b0JBQ1Q7O1FBQ0Y7NkRBQUcsRUFBRTtJQUVMOztHQUVDLEdBQ0QsTUFBTWdGLGFBQWFoSCxrREFBV0E7d0RBQUM7WUFDN0JzQztnRUFBU08sQ0FBQUEsT0FBUzt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFYixPQUFPO29CQUFLOztRQUMzQzt1REFBRyxFQUFFO0lBRUw7O0dBRUMsR0FDRCxNQUFNaUYsMEJBQTBCakgsa0RBQVdBO3FFQUFDO1lBQzFDLElBQUksQ0FBQ3FDLE1BQU1JLFdBQVcsRUFBRSxPQUFPO1lBRS9CLE1BQU1pQixPQUFPckIsTUFBTUksV0FBVyxDQUFDQSxXQUFXO1lBQzFDLE9BQU87Z0JBQ0xVLFFBQVFELFdBQVdRLEtBQUtQLE1BQU0sSUFBSTtnQkFDbENDLFVBQVVNLEtBQUtOLFFBQVEsS0FBSyxJQUFJLFFBQVE7Z0JBQ3hDa0QsaUJBQWlCcEQsV0FBV1EsS0FBS0csaUJBQWlCLElBQUk7Z0JBQ3REMEMsY0FBY3JELFdBQVdRLEtBQUtJLGFBQWEsSUFBSTtnQkFDL0MwQyxjQUFjOUMsS0FBS0ssYUFBYTtnQkFDaENDLFdBQVcsSUFBSTJCLEtBQUtqQyxLQUFLTSxTQUFTLEdBQUc7Z0JBQ3JDQyxPQUFPUCxLQUFLTyxLQUFLO1lBQ25CO1FBQ0Y7b0VBQUc7UUFBQzVCLE1BQU1JLFdBQVc7S0FBQztJQUV0QixPQUFPO1FBQ0wsR0FBR0osS0FBSztRQUNSSztRQUNBZTtRQUNBc0Q7UUFDQUM7UUFDQUM7UUFDQUMsYUFBYSxDQUFDLEVBQUMvRSxtQkFBQUEsOEJBQUFBLGtCQUFBQSxPQUFRUyxPQUFPLGNBQWZULHNDQUFBQSxnQkFBaUJQLE9BQU87SUFDekM7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9zcmMvaG9va3MvdXNlU2lnbmF0dXJlUHVyY2hhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlVG9uV2FsbGV0LCB1c2VUb25Db25uZWN0VUkgfSBmcm9tICdAdG9uY29ubmVjdC91aS1yZWFjdCdcbmltcG9ydCB7IGJlZ2luQ2VsbCwgdG9OYW5vLCBBZGRyZXNzLCBDZWxsIH0gZnJvbSAnQHRvbi9jb3JlJ1xuaW1wb3J0IHsgQXBpU2VydmljZSwgUHVyY2hhc2VDYWxjdWxhdGlvblJlc3BvbnNlLCBoYW5kbGVBcGlFcnJvciB9IGZyb20gJ0AvbGliL2FwaVNlcnZpY2UnXG5pbXBvcnQgeyBidWlsZFVTRFRGb3J3YXJkUGF5bG9hZCB9IGZyb20gJ0AvbGliL3VzZHRQdXJjaGFzZSdcblxuLy8gQ29udHJhY3QgY29uZmlndXJhdGlvbiAtIGluIHByb2R1Y3Rpb24gdGhpcyBzaG91bGQgY29tZSBmcm9tIGVudmlyb25tZW50IHZhcmlhYmxlc1xuY29uc3QgQ09OVFJBQ1RfQUREUkVTUyA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FVQ1RJT05fQ09OVFJBQ1RfQUREUkVTUyB8fCAnRVFDLi4uJyAvLyBSZXBsYWNlIHdpdGggYWN0dWFsIGNvbnRyYWN0IGFkZHJlc3NcbmNvbnN0IFVTRFRfSkVUVE9OX01BU1RFUl9BRERSRVNTID0gJ2tRQXpmdDNleHNxOTQ2ZU85MmVPRjBRa1FxTkZPTGFQSGFrMThYZHk0T1lHOVdqTicgLy8gVGVzdG5ldCBVU0RUIGpldHRvbiBtYXN0ZXJcblxuY29uc29sZS5sb2coJ1VzaW5nIGF1Y3Rpb24gY29udHJhY3QgYWRkcmVzczonLCBDT05UUkFDVF9BRERSRVNTKVxuY29uc29sZS5sb2coJ1VzaW5nIFVTRFQgamV0dG9uIG1hc3RlciBhZGRyZXNzOicsIFVTRFRfSkVUVE9OX01BU1RFUl9BRERSRVNTKVxuXG5leHBvcnQgaW50ZXJmYWNlIFNpZ25hdHVyZVB1cmNoYXNlU3RhdGUge1xuICBpc0NhbGN1bGF0aW5nOiBib29sZWFuXG4gIGlzUHJvY2Vzc2luZzogYm9vbGVhblxuICBjYWxjdWxhdGlvbjogUHVyY2hhc2VDYWxjdWxhdGlvblJlc3BvbnNlIHwgbnVsbFxuICBlcnJvcjogc3RyaW5nIHwgbnVsbFxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFB1cmNoYXNlUGFyYW1zIHtcbiAgYW1vdW50OiBzdHJpbmdcbiAgY3VycmVuY3k6ICdUT04nIHwgJ1VTRFQnXG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBjYWxjdWxhdGUgamV0dG9uIHdhbGxldCBhZGRyZXNzXG5hc3luYyBmdW5jdGlvbiBnZXRKZXR0b25XYWxsZXRBZGRyZXNzKG93bmVyQWRkcmVzczogc3RyaW5nLCBqZXR0b25NYXN0ZXJBZGRyZXNzOiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xuICB0cnkge1xuICAgIC8vIFVzZSBUb25XZWIgdG8gY2FsY3VsYXRlIGpldHRvbiB3YWxsZXQgYWRkcmVzc1xuICAgIGNvbnN0IFRvbldlYiA9IChhd2FpdCBpbXBvcnQoJ3RvbndlYicpKS5kZWZhdWx0XG4gICAgY29uc3QgdG9ud2ViID0gbmV3IFRvbldlYihuZXcgVG9uV2ViLkh0dHBQcm92aWRlcignaHR0cHM6Ly90ZXN0bmV0LnRvbmNlbnRlci5jb20vYXBpL3YyL2pzb25SUEMnKSlcblxuICAgIC8vQHRzLWlnbm9yZVxuICAgIGNvbnN0IGpldHRvbk1hc3RlckNvbnRyYWN0ID0gbmV3IFRvbldlYi50b2tlbi5qZXR0b24uSmV0dG9uTWludGVyKHRvbndlYi5wcm92aWRlciwge1xuICAgICAgYWRkcmVzczogamV0dG9uTWFzdGVyQWRkcmVzc1xuICAgIH0pXG5cbiAgICBjb25zdCBqZXR0b25XYWxsZXRBZGRyZXNzID0gYXdhaXQgamV0dG9uTWFzdGVyQ29udHJhY3QuZ2V0SmV0dG9uV2FsbGV0QWRkcmVzcyhcbiAgICAgIG5ldyBUb25XZWIudXRpbHMuQWRkcmVzcyhvd25lckFkZHJlc3MpXG4gICAgKVxuICAgIHJldHVybiBqZXR0b25XYWxsZXRBZGRyZXNzLnRvU3RyaW5nKClcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2V0IGpldHRvbiB3YWxsZXQgYWRkcmVzczonLCBlcnJvcilcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBjYWxjdWxhdGUgVVNEVCB3YWxsZXQgYWRkcmVzcycpXG4gIH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVNpZ25hdHVyZVB1cmNoYXNlKCkge1xuICBjb25zdCB3YWxsZXQgPSB1c2VUb25XYWxsZXQoKVxuICBjb25zdCBbdG9uQ29ubmVjdFVJXSA9IHVzZVRvbkNvbm5lY3RVSSgpXG4gIGNvbnN0IFtzdGF0ZSwgc2V0U3RhdGVdID0gdXNlU3RhdGU8U2lnbmF0dXJlUHVyY2hhc2VTdGF0ZT4oe1xuICAgIGlzQ2FsY3VsYXRpbmc6IGZhbHNlLFxuICAgIGlzUHJvY2Vzc2luZzogZmFsc2UsXG4gICAgY2FsY3VsYXRpb246IG51bGwsXG4gICAgZXJyb3I6IG51bGxcbiAgfSlcblxuICAvKipcbiAgICogQ2FsY3VsYXRlIHB1cmNoYXNlIHdpdGggc2lnbmF0dXJlIHZlcmlmaWNhdGlvblxuICAgKiBUaGlzIGlzIHRoZSBvbmx5IHN1cHBvcnRlZCBwdXJjaGFzZSBtZXRob2QgLSBhbGwgcHVyY2hhc2VzIG11c3QgYmUgc2lnbmF0dXJlIHZlcmlmaWVkXG4gICAqL1xuICBjb25zdCBjYWxjdWxhdGVQdXJjaGFzZSA9IHVzZUNhbGxiYWNrKGFzeW5jIChwYXJhbXM6IFB1cmNoYXNlUGFyYW1zKSA9PiB7XG4gICAgaWYgKCF3YWxsZXQ/LmFjY291bnQ/LmFkZHJlc3MpIHtcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHsgLi4ucHJldiwgZXJyb3I6ICdXYWxsZXQgbm90IGNvbm5lY3RlZCcgfSkpXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cblxuICAgIHNldFN0YXRlKHByZXYgPT4gKHsgXG4gICAgICAuLi5wcmV2LCBcbiAgICAgIGlzQ2FsY3VsYXRpbmc6IHRydWUsIFxuICAgICAgZXJyb3I6IG51bGwsXG4gICAgICBjYWxjdWxhdGlvbjogbnVsbCBcbiAgICB9KSlcblxuICAgIHRyeSB7XG4gICAgICAvLyBDb252ZXJ0IGFtb3VudCB0byBuYW5vdG9ucy9zbWFsbGVzdCB1bml0XG4gICAgICBjb25zdCBhbW91bnRCaWdJbnQgPSBCaWdJbnQoTWF0aC5mbG9vcihwYXJzZUZsb2F0KHBhcmFtcy5hbW91bnQpICogMTAwMDAwMDAwMCkpXG4gICAgICBjb25zdCBjdXJyZW5jeSA9IHBhcmFtcy5jdXJyZW5jeSA9PT0gJ1RPTicgPyAwIDogMVxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IEFwaVNlcnZpY2UuY2FsY3VsYXRlUHVyY2hhc2Uoe1xuICAgICAgICB1c2VyX2FkZHJlc3M6IHdhbGxldC5hY2NvdW50LmFkZHJlc3MsXG4gICAgICAgIGFtb3VudDogYW1vdW50QmlnSW50LnRvU3RyaW5nKCksXG4gICAgICAgIGN1cnJlbmN5XG4gICAgICB9KVxuXG4gICAgICBpZiAoIXJlc3BvbnNlLnN1Y2Nlc3MpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmVycm9yIHx8ICdDYWxjdWxhdGlvbiBmYWlsZWQnKVxuICAgICAgfVxuXG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IFxuICAgICAgICAuLi5wcmV2LCBcbiAgICAgICAgaXNDYWxjdWxhdGluZzogZmFsc2UsXG4gICAgICAgIGNhbGN1bGF0aW9uOiByZXNwb25zZVxuICAgICAgfSkpXG5cbiAgICAgIHJldHVybiByZXNwb25zZVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBoYW5kbGVBcGlFcnJvcihlcnJvcilcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHsgXG4gICAgICAgIC4uLnByZXYsIFxuICAgICAgICBpc0NhbGN1bGF0aW5nOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6IGVycm9yTWVzc2FnZVxuICAgICAgfSkpXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cbiAgfSwgW3dhbGxldF0pXG5cbiAgLyoqXG4gICAqIEV4ZWN1dGUgcHVyY2hhc2Ugd2l0aCBzaWduYXR1cmUgdmVyaWZpY2F0aW9uXG4gICAqIEFsbCBwdXJjaGFzZXMgbXVzdCBnbyB0aHJvdWdoIHRoaXMgc2lnbmF0dXJlIHZlcmlmaWNhdGlvbiBwcm9jZXNzXG4gICAqL1xuICBjb25zdCBleGVjdXRlUHVyY2hhc2UgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF3YWxsZXQgfHwgIXN0YXRlLmNhbGN1bGF0aW9uIHx8ICF0b25Db25uZWN0VUkpIHtcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHsgLi4ucHJldiwgZXJyb3I6ICdObyBjYWxjdWxhdGlvbiBhdmFpbGFibGUgb3Igd2FsbGV0IG5vdCBjb25uZWN0ZWQnIH0pKVxuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuXG4gICAgc2V0U3RhdGUocHJldiA9PiAoeyAuLi5wcmV2LCBpc1Byb2Nlc3Npbmc6IHRydWUsIGVycm9yOiBudWxsIH0pKVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNhbGMgPSBzdGF0ZS5jYWxjdWxhdGlvbi5jYWxjdWxhdGlvblxuICAgICAgY29uc3Qgc2lnbmF0dXJlID0gc3RhdGUuY2FsY3VsYXRpb24uc2lnbmF0dXJlXG5cbiAgICAgIGNvbnNvbGUubG9nKCdFeGVjdXRpbmcgcHVyY2hhc2Ugd2l0aCBzaWduYXR1cmU6Jywge1xuICAgICAgICBjYWxjdWxhdGlvbjogY2FsYyxcbiAgICAgICAgc2lnbmF0dXJlOiBzaWduYXR1cmVcbiAgICAgIH0pXG5cbiAgICAgIGNvbnNvbGUubG9nKCdDb250cmFjdCBhZGRyZXNzOicsIENPTlRSQUNUX0FERFJFU1MpXG4gICAgICBjb25zb2xlLmxvZygnQ2FsY3VsYXRpb24gZGV0YWlsczonLCB7XG4gICAgICAgIHVzZXI6IGNhbGMudXNlcixcbiAgICAgICAgYW1vdW50OiBjYWxjLmFtb3VudCxcbiAgICAgICAgY3VycmVuY3k6IGNhbGMuY3VycmVuY3ksXG4gICAgICAgIHRva2Vuc190b19yZWNlaXZlOiBjYWxjLnRva2Vuc190b19yZWNlaXZlLFxuICAgICAgICBjdXJyZW50X3ByaWNlOiBjYWxjLmN1cnJlbnRfcHJpY2UsXG4gICAgICAgIGN1cnJlbnRfcm91bmQ6IGNhbGMuY3VycmVudF9yb3VuZCxcbiAgICAgICAgdGltZXN0YW1wOiBjYWxjLnRpbWVzdGFtcCxcbiAgICAgICAgbm9uY2U6IGNhbGMubm9uY2VcbiAgICAgIH0pXG5cbiAgICAgIC8vIEhhbmRsZSBkaWZmZXJlbnQgY3VycmVuY2llc1xuICAgICAgaWYgKGNhbGMuY3VycmVuY3kgPT09IDApIHtcbiAgICAgICAgLy8gVE9OIHB1cmNoYXNlIC0gZGlyZWN0IGNvbnRyYWN0IGNhbGxcbiAgICAgICAgcmV0dXJuIGF3YWl0IGV4ZWN1dGVUT05QdXJjaGFzZShjYWxjLCBzaWduYXR1cmUpXG4gICAgICB9IGVsc2UgaWYgKGNhbGMuY3VycmVuY3kgPT09IDEpIHtcbiAgICAgICAgLy8gVVNEVCBwdXJjaGFzZSAtIGpldHRvbiB0cmFuc2ZlclxuICAgICAgICByZXR1cm4gYXdhaXQgZXhlY3V0ZVVTRFRQdXJjaGFzZShjYWxjLCBzaWduYXR1cmUpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1Vuc3VwcG9ydGVkIGN1cnJlbmN5JylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignVHJhbnNhY3Rpb24gZmFpbGVkOicsIGVycm9yKVxuICAgICAgbGV0IGVycm9yTWVzc2FnZSA9ICdUcmFuc2FjdGlvbiBmYWlsZWQnXG5cbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdVc2VyIHJlamVjdGVkJykpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAnVHJhbnNhY3Rpb24gd2FzIGNhbmNlbGxlZCBieSB1c2VyJ1xuICAgICAgICB9IGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ0luc3VmZmljaWVudCBmdW5kcycpKSB7XG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gJ0luc3VmZmljaWVudCBmdW5kcyBmb3IgdHJhbnNhY3Rpb24nXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3IubWVzc2FnZVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSBoYW5kbGVBcGlFcnJvcihlcnJvcilcbiAgICAgIH1cblxuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBpc1Byb2Nlc3Npbmc6IGZhbHNlLFxuICAgICAgICBlcnJvcjogZXJyb3JNZXNzYWdlXG4gICAgICB9KSlcbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH1cbiAgfSwgW3dhbGxldCwgc3RhdGUuY2FsY3VsYXRpb24sIHRvbkNvbm5lY3RVSV0pXG5cbiAgLyoqXG4gICAqIEV4ZWN1dGUgVE9OIHB1cmNoYXNlIHdpdGggc2lnbmF0dXJlIHZlcmlmaWNhdGlvblxuICAgKi9cbiAgY29uc3QgZXhlY3V0ZVRPTlB1cmNoYXNlID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNhbGM6IGFueSwgc2lnbmF0dXJlOiBzdHJpbmcpID0+IHtcbiAgICAvLyBDb252ZXJ0IHNpZ25hdHVyZSBmcm9tIGJhc2U2NCB0byBidWZmZXJcbiAgICBjb25zdCBzaWduYXR1cmVCdWZmZXIgPSBCdWZmZXIuZnJvbShzaWduYXR1cmUsICdiYXNlNjQnKVxuXG4gICAgLy8gQ3JlYXRlIHNpZ25hdHVyZSBjZWxsXG4gICAgY29uc3Qgc2lnbmF0dXJlQ2VsbCA9IGJlZ2luQ2VsbCgpXG4gICAgICAuc3RvcmVCdWZmZXIoc2lnbmF0dXJlQnVmZmVyKVxuICAgICAgLmVuZENlbGwoKVxuXG4gICAgLy8gQ3JlYXRlIHRoZSBQdXJjaGFzZVdpdGhTaWduYXR1cmUgbWVzc2FnZSBib2R5IGZvbGxvd2luZyB0aGUgZXhhY3QgZ2VuZXJhdGVkIHN0cnVjdHVyZVxuICAgIGNvbnN0IHB1cmNoYXNlQm9keSA9IGJlZ2luQ2VsbCgpXG4gICAgICAuc3RvcmVVaW50KDE1MjQ3NzA4MjAsIDMyKSAvLyBQdXJjaGFzZVdpdGhTaWduYXR1cmUgb3AgY29kZVxuICAgICAgLy8gU3RvcmUgUHVyY2hhc2VDYWxjdWxhdGlvbiBpbmxpbmUgKG5vdCBhcyBhIHNlcGFyYXRlIGNlbGwpXG4gICAgICAuc3RvcmVBZGRyZXNzKEFkZHJlc3MucGFyc2UoY2FsYy51c2VyKSlcbiAgICAgIC5zdG9yZUNvaW5zKEJpZ0ludChjYWxjLmFtb3VudCkpXG4gICAgICAuc3RvcmVVaW50KGNhbGMuY3VycmVuY3ksIDgpXG4gICAgICAuc3RvcmVDb2lucyhCaWdJbnQoY2FsYy50b2tlbnNfdG9fcmVjZWl2ZSkpXG4gICAgICAuc3RvcmVDb2lucyhCaWdJbnQoY2FsYy5jdXJyZW50X3ByaWNlKSlcbiAgICAgIC5zdG9yZVVpbnQoY2FsYy5jdXJyZW50X3JvdW5kLCAzMilcbiAgICAgIC5zdG9yZVVpbnQoY2FsYy50aW1lc3RhbXAsIDY0KVxuICAgICAgLnN0b3JlVWludChCaWdJbnQoY2FsYy5ub25jZSksIDY0KVxuICAgICAgLy8gU3RvcmUgc2lnbmF0dXJlIGFzIHJlZmVyZW5jZVxuICAgICAgLnN0b3JlUmVmKHNpZ25hdHVyZUNlbGwpXG4gICAgICAuZW5kQ2VsbCgpXG5cbiAgICBjb25zb2xlLmxvZygnTWVzc2FnZSBzdHJ1Y3R1cmU6Jywge1xuICAgICAgb3BDb2RlOiAnMHgnICsgKDE1MjQ3NzA4MjApLnRvU3RyaW5nKDE2KSxcbiAgICAgIHNpZ25hdHVyZUNlbGxCaXRzOiBzaWduYXR1cmVDZWxsLmJpdHMubGVuZ3RoLFxuICAgICAgdG90YWxCb2R5Qml0czogcHVyY2hhc2VCb2R5LmJpdHMubGVuZ3RoLFxuICAgICAgYm9keUJvY0xlbmd0aDogcHVyY2hhc2VCb2R5LnRvQm9jKCkubGVuZ3RoXG4gICAgfSlcblxuICAgIC8vIENyZWF0ZSB0cmFuc2FjdGlvbiByZXF1ZXN0XG4gICAgY29uc3QgdHJhbnNhY3Rpb25SZXF1ZXN0ID0ge1xuICAgICAgdmFsaWRVbnRpbDogTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCkgKyA2MDAsIC8vIDEwIG1pbnV0ZXMgdmFsaWRpdHlcbiAgICAgIG1lc3NhZ2VzOiBbXG4gICAgICAgIHtcbiAgICAgICAgICBhZGRyZXNzOiBDT05UUkFDVF9BRERSRVNTLFxuICAgICAgICAgIGFtb3VudDogdG9OYW5vKCcwLjUnKS50b1N0cmluZygpLCAvLyBHYXMgZmVlIGZvciB0cmFuc2FjdGlvblxuICAgICAgICAgIHBheWxvYWQ6IEJ1ZmZlci5mcm9tKHB1cmNoYXNlQm9keS50b0JvYygpKS50b1N0cmluZygnYmFzZTY0JylcbiAgICAgICAgfVxuICAgICAgXVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdUcmFuc2FjdGlvbiByZXF1ZXN0OicsIHtcbiAgICAgIGNvbnRyYWN0QWRkcmVzczogQ09OVFJBQ1RfQUREUkVTUyxcbiAgICAgIGdhc0Ftb3VudDogdG9OYW5vKCcwLjUnKS50b1N0cmluZygpLFxuICAgICAgdmFsaWRVbnRpbDogdHJhbnNhY3Rpb25SZXF1ZXN0LnZhbGlkVW50aWwsXG4gICAgICBwYXlsb2FkTGVuZ3RoOiB0cmFuc2FjdGlvblJlcXVlc3QubWVzc2FnZXNbMF0ucGF5bG9hZC5sZW5ndGhcbiAgICB9KVxuXG4gICAgLy8gU2VuZCB0cmFuc2FjdGlvbiB1c2luZyBUT04gQ29ubmVjdCBVSVxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRvbkNvbm5lY3RVSS5zZW5kVHJhbnNhY3Rpb24odHJhbnNhY3Rpb25SZXF1ZXN0KVxuICAgIGNvbnNvbGUubG9nKCdUT04gVHJhbnNhY3Rpb24gc2VudDonLCByZXN1bHQpXG5cbiAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgaXNQcm9jZXNzaW5nOiBmYWxzZSxcbiAgICAgIGNhbGN1bGF0aW9uOiBudWxsIC8vIENsZWFyIGFmdGVyIHN1Y2Nlc3NmdWwgcHVyY2hhc2VcbiAgICB9KSlcblxuICAgIHJldHVybiB0cnVlXG4gIH0sIFt0b25Db25uZWN0VUldKVxuXG4gIC8qKlxuICAgKiBFeGVjdXRlIFVTRFQgcHVyY2hhc2Ugd2l0aCBzaWduYXR1cmUgdmVyaWZpY2F0aW9uXG4gICAqL1xuICBjb25zdCBleGVjdXRlVVNEVFB1cmNoYXNlID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNhbGM6IGFueSwgc2lnbmF0dXJlOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXdhbGxldD8uYWNjb3VudD8uYWRkcmVzcykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdXYWxsZXQgbm90IGNvbm5lY3RlZCcpXG4gICAgfVxuXG4gICAgLy8gR2V0IHVzZXIncyBVU0RUIGpldHRvbiB3YWxsZXQgYWRkcmVzc1xuICAgIGNvbnN0IHVzZXJVU0RUV2FsbGV0QWRkcmVzcyA9IGF3YWl0IGdldEpldHRvbldhbGxldEFkZHJlc3MoXG4gICAgICB3YWxsZXQuYWNjb3VudC5hZGRyZXNzLFxuICAgICAgVVNEVF9KRVRUT05fTUFTVEVSX0FERFJFU1NcbiAgICApXG5cbiAgICBjb25zb2xlLmxvZygnVXNlciBVU0RUIHdhbGxldCBhZGRyZXNzOicsIHVzZXJVU0RUV2FsbGV0QWRkcmVzcylcblxuICAgIC8vIEJ1aWxkIGZvcndhcmQgcGF5bG9hZCB3aXRoIHNpZ25hdHVyZSB2ZXJpZmljYXRpb24gZGF0YVxuICAgIGNvbnN0IHB1cmNoYXNlQ2FsYyA9IHtcbiAgICAgIHVzZXI6IEFkZHJlc3MucGFyc2UoY2FsYy51c2VyKSxcbiAgICAgIGFtb3VudDogQmlnSW50KGNhbGMuYW1vdW50KSxcbiAgICAgIGN1cnJlbmN5OiBjYWxjLmN1cnJlbmN5LFxuICAgICAgdG9rZW5zVG9SZWNlaXZlOiBCaWdJbnQoY2FsYy50b2tlbnNfdG9fcmVjZWl2ZSksXG4gICAgICBjdXJyZW50UHJpY2U6IEJpZ0ludChjYWxjLmN1cnJlbnRfcHJpY2UpLFxuICAgICAgY3VycmVudFJvdW5kOiBjYWxjLmN1cnJlbnRfcm91bmQsXG4gICAgICB0aW1lc3RhbXA6IGNhbGMudGltZXN0YW1wLFxuICAgICAgbm9uY2U6IEJpZ0ludChjYWxjLm5vbmNlKVxuICAgIH1cbiAgICBjb25zdCBmb3J3YXJkUGF5bG9hZCA9IGJ1aWxkVVNEVEZvcndhcmRQYXlsb2FkKHB1cmNoYXNlQ2FsYywgc2lnbmF0dXJlKVxuXG4gICAgLy8gQ3JlYXRlIGpldHRvbiB0cmFuc2ZlciBtZXNzYWdlXG4gICAgY29uc3QgamV0dG9uVHJhbnNmZXJCb2R5ID0gYmVnaW5DZWxsKClcbiAgICAgIC5zdG9yZVVpbnQoMHhmOGE3ZWE1LCAzMikgLy8gSmV0dG9uVHJhbnNmZXIgb3AgY29kZVxuICAgICAgLnN0b3JlVWludCgwLCA2NCkgLy8gcXVlcnlfaWRcbiAgICAgIC5zdG9yZUNvaW5zKEJpZ0ludChjYWxjLmFtb3VudCkpIC8vIGFtb3VudFxuICAgICAgLnN0b3JlQWRkcmVzcyhBZGRyZXNzLnBhcnNlKENPTlRSQUNUX0FERFJFU1MpKSAvLyBkZXN0aW5hdGlvbiAoYXVjdGlvbiBjb250cmFjdClcbiAgICAgIC5zdG9yZUFkZHJlc3MoQWRkcmVzcy5wYXJzZSh3YWxsZXQuYWNjb3VudC5hZGRyZXNzKSkgLy8gcmVzcG9uc2VfZGVzdGluYXRpb25cbiAgICAgIC5zdG9yZU1heWJlUmVmKG51bGwpIC8vIGN1c3RvbV9wYXlsb2FkXG4gICAgICAuc3RvcmVDb2lucyh0b05hbm8oJzAuMDUnKSkgLy8gZm9yd2FyZF90b25fYW1vdW50IChnYXMgZm9yIHNpZ25hdHVyZSB2ZXJpZmljYXRpb24pXG4gICAgICAuc3RvcmVNYXliZVJlZihmb3J3YXJkUGF5bG9hZCkgLy8gZm9yd2FyZF9wYXlsb2FkIHdpdGggc2lnbmF0dXJlIGRhdGFcbiAgICAgIC5lbmRDZWxsKClcblxuICAgIGNvbnNvbGUubG9nKCdVU0RUIFRyYW5zZmVyIGRldGFpbHM6Jywge1xuICAgICAgdXNlcldhbGxldDogdXNlclVTRFRXYWxsZXRBZGRyZXNzLFxuICAgICAgYW1vdW50OiBjYWxjLmFtb3VudCxcbiAgICAgIGRlc3RpbmF0aW9uOiBDT05UUkFDVF9BRERSRVNTLFxuICAgICAgZm9yd2FyZFBheWxvYWRTaXplOiBmb3J3YXJkUGF5bG9hZC50b0JvYygpLmxlbmd0aFxuICAgIH0pXG5cbiAgICAvLyBDcmVhdGUgdHJhbnNhY3Rpb24gcmVxdWVzdCBmb3IgVVNEVCB0cmFuc2ZlclxuICAgIGNvbnN0IHRyYW5zYWN0aW9uUmVxdWVzdCA9IHtcbiAgICAgIHZhbGlkVW50aWw6IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApICsgNjAwLCAvLyAxMCBtaW51dGVzIHZhbGlkaXR5XG4gICAgICBtZXNzYWdlczogW1xuICAgICAgICB7XG4gICAgICAgICAgYWRkcmVzczogdXNlclVTRFRXYWxsZXRBZGRyZXNzLFxuICAgICAgICAgIGFtb3VudDogdG9OYW5vKCcwLjEnKS50b1N0cmluZygpLCAvLyBHYXMgZmVlIGZvciBqZXR0b24gdHJhbnNmZXJcbiAgICAgICAgICBwYXlsb2FkOiBCdWZmZXIuZnJvbShqZXR0b25UcmFuc2ZlckJvZHkudG9Cb2MoKSkudG9TdHJpbmcoJ2Jhc2U2NCcpXG4gICAgICAgIH1cbiAgICAgIF1cbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygnVVNEVCBUcmFuc2FjdGlvbiByZXF1ZXN0OicsIHtcbiAgICAgIGpldHRvbldhbGxldEFkZHJlc3M6IHVzZXJVU0RUV2FsbGV0QWRkcmVzcyxcbiAgICAgIGdhc0Ftb3VudDogdG9OYW5vKCcwLjEnKS50b1N0cmluZygpLFxuICAgICAgdmFsaWRVbnRpbDogdHJhbnNhY3Rpb25SZXF1ZXN0LnZhbGlkVW50aWwsXG4gICAgICBwYXlsb2FkTGVuZ3RoOiB0cmFuc2FjdGlvblJlcXVlc3QubWVzc2FnZXNbMF0ucGF5bG9hZC5sZW5ndGhcbiAgICB9KVxuXG4gICAgLy8gU2VuZCB0cmFuc2FjdGlvbiB1c2luZyBUT04gQ29ubmVjdCBVSVxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRvbkNvbm5lY3RVSS5zZW5kVHJhbnNhY3Rpb24odHJhbnNhY3Rpb25SZXF1ZXN0KVxuICAgIGNvbnNvbGUubG9nKCdVU0RUIFRyYW5zYWN0aW9uIHNlbnQ6JywgcmVzdWx0KVxuXG4gICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGlzUHJvY2Vzc2luZzogZmFsc2UsXG4gICAgICBjYWxjdWxhdGlvbjogbnVsbCAvLyBDbGVhciBhZnRlciBzdWNjZXNzZnVsIHB1cmNoYXNlXG4gICAgfSkpXG5cbiAgICByZXR1cm4gdHJ1ZVxuICB9LCBbd2FsbGV0LCB0b25Db25uZWN0VUldKVxuXG4gIC8qKlxuICAgKiBDbGVhciBjdXJyZW50IGNhbGN1bGF0aW9uXG4gICAqL1xuICBjb25zdCBjbGVhckNhbGN1bGF0aW9uID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldFN0YXRlKHByZXYgPT4gKHsgXG4gICAgICAuLi5wcmV2LCBcbiAgICAgIGNhbGN1bGF0aW9uOiBudWxsLFxuICAgICAgZXJyb3I6IG51bGxcbiAgICB9KSlcbiAgfSwgW10pXG5cbiAgLyoqXG4gICAqIENsZWFyIGVycm9yXG4gICAqL1xuICBjb25zdCBjbGVhckVycm9yID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldFN0YXRlKHByZXYgPT4gKHsgLi4ucHJldiwgZXJyb3I6IG51bGwgfSkpXG4gIH0sIFtdKVxuXG4gIC8qKlxuICAgKiBHZXQgZm9ybWF0dGVkIGNhbGN1bGF0aW9uIGRhdGEgZm9yIGRpc3BsYXlcbiAgICovXG4gIGNvbnN0IGdldEZvcm1hdHRlZENhbGN1bGF0aW9uID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICghc3RhdGUuY2FsY3VsYXRpb24pIHJldHVybiBudWxsXG5cbiAgICBjb25zdCBjYWxjID0gc3RhdGUuY2FsY3VsYXRpb24uY2FsY3VsYXRpb25cbiAgICByZXR1cm4ge1xuICAgICAgYW1vdW50OiBwYXJzZUZsb2F0KGNhbGMuYW1vdW50KSAvIDEwMDAwMDAwMDAsIC8vIENvbnZlcnQgZnJvbSBuYW5vdG9uc1xuICAgICAgY3VycmVuY3k6IGNhbGMuY3VycmVuY3kgPT09IDAgPyAnVE9OJyA6ICdVU0RUJyxcbiAgICAgIHRva2Vuc1RvUmVjZWl2ZTogcGFyc2VGbG9hdChjYWxjLnRva2Vuc190b19yZWNlaXZlKSAvIDEwMDAwMDAwMDAsXG4gICAgICBjdXJyZW50UHJpY2U6IHBhcnNlRmxvYXQoY2FsYy5jdXJyZW50X3ByaWNlKSAvIDEwMDAwMDAwMDAsXG4gICAgICBjdXJyZW50Um91bmQ6IGNhbGMuY3VycmVudF9yb3VuZCxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoY2FsYy50aW1lc3RhbXAgKiAxMDAwKSxcbiAgICAgIG5vbmNlOiBjYWxjLm5vbmNlXG4gICAgfVxuICB9LCBbc3RhdGUuY2FsY3VsYXRpb25dKVxuXG4gIHJldHVybiB7XG4gICAgLi4uc3RhdGUsXG4gICAgY2FsY3VsYXRlUHVyY2hhc2UsXG4gICAgZXhlY3V0ZVB1cmNoYXNlLFxuICAgIGNsZWFyQ2FsY3VsYXRpb24sXG4gICAgY2xlYXJFcnJvcixcbiAgICBnZXRGb3JtYXR0ZWRDYWxjdWxhdGlvbixcbiAgICBpc0Nvbm5lY3RlZDogISF3YWxsZXQ/LmFjY291bnQ/LmFkZHJlc3NcbiAgfVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VUb25XYWxsZXQiLCJ1c2VUb25Db25uZWN0VUkiLCJiZWdpbkNlbGwiLCJ0b05hbm8iLCJBZGRyZXNzIiwiQXBpU2VydmljZSIsImhhbmRsZUFwaUVycm9yIiwiYnVpbGRVU0RURm9yd2FyZFBheWxvYWQiLCJDT05UUkFDVF9BRERSRVNTIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FVQ1RJT05fQ09OVFJBQ1RfQUREUkVTUyIsIlVTRFRfSkVUVE9OX01BU1RFUl9BRERSRVNTIiwiY29uc29sZSIsImxvZyIsImdldEpldHRvbldhbGxldEFkZHJlc3MiLCJvd25lckFkZHJlc3MiLCJqZXR0b25NYXN0ZXJBZGRyZXNzIiwiVG9uV2ViIiwiZGVmYXVsdCIsInRvbndlYiIsIkh0dHBQcm92aWRlciIsImpldHRvbk1hc3RlckNvbnRyYWN0IiwidG9rZW4iLCJqZXR0b24iLCJKZXR0b25NaW50ZXIiLCJwcm92aWRlciIsImFkZHJlc3MiLCJqZXR0b25XYWxsZXRBZGRyZXNzIiwidXRpbHMiLCJ0b1N0cmluZyIsImVycm9yIiwiRXJyb3IiLCJ1c2VTaWduYXR1cmVQdXJjaGFzZSIsIndhbGxldCIsInRvbkNvbm5lY3RVSSIsInN0YXRlIiwic2V0U3RhdGUiLCJpc0NhbGN1bGF0aW5nIiwiaXNQcm9jZXNzaW5nIiwiY2FsY3VsYXRpb24iLCJjYWxjdWxhdGVQdXJjaGFzZSIsInBhcmFtcyIsImFjY291bnQiLCJwcmV2IiwiYW1vdW50QmlnSW50IiwiQmlnSW50IiwiTWF0aCIsImZsb29yIiwicGFyc2VGbG9hdCIsImFtb3VudCIsImN1cnJlbmN5IiwicmVzcG9uc2UiLCJ1c2VyX2FkZHJlc3MiLCJzdWNjZXNzIiwiZXJyb3JNZXNzYWdlIiwiZXhlY3V0ZVB1cmNoYXNlIiwiY2FsYyIsInNpZ25hdHVyZSIsInVzZXIiLCJ0b2tlbnNfdG9fcmVjZWl2ZSIsImN1cnJlbnRfcHJpY2UiLCJjdXJyZW50X3JvdW5kIiwidGltZXN0YW1wIiwibm9uY2UiLCJleGVjdXRlVE9OUHVyY2hhc2UiLCJleGVjdXRlVVNEVFB1cmNoYXNlIiwibWVzc2FnZSIsImluY2x1ZGVzIiwic2lnbmF0dXJlQnVmZmVyIiwiQnVmZmVyIiwiZnJvbSIsInNpZ25hdHVyZUNlbGwiLCJzdG9yZUJ1ZmZlciIsImVuZENlbGwiLCJwdXJjaGFzZUJvZHkiLCJzdG9yZVVpbnQiLCJzdG9yZUFkZHJlc3MiLCJwYXJzZSIsInN0b3JlQ29pbnMiLCJzdG9yZVJlZiIsIm9wQ29kZSIsInNpZ25hdHVyZUNlbGxCaXRzIiwiYml0cyIsImxlbmd0aCIsInRvdGFsQm9keUJpdHMiLCJib2R5Qm9jTGVuZ3RoIiwidG9Cb2MiLCJ0cmFuc2FjdGlvblJlcXVlc3QiLCJ2YWxpZFVudGlsIiwiRGF0ZSIsIm5vdyIsIm1lc3NhZ2VzIiwicGF5bG9hZCIsImNvbnRyYWN0QWRkcmVzcyIsImdhc0Ftb3VudCIsInBheWxvYWRMZW5ndGgiLCJyZXN1bHQiLCJzZW5kVHJhbnNhY3Rpb24iLCJ1c2VyVVNEVFdhbGxldEFkZHJlc3MiLCJwdXJjaGFzZUNhbGMiLCJ0b2tlbnNUb1JlY2VpdmUiLCJjdXJyZW50UHJpY2UiLCJjdXJyZW50Um91bmQiLCJmb3J3YXJkUGF5bG9hZCIsImpldHRvblRyYW5zZmVyQm9keSIsInN0b3JlTWF5YmVSZWYiLCJ1c2VyV2FsbGV0IiwiZGVzdGluYXRpb24iLCJmb3J3YXJkUGF5bG9hZFNpemUiLCJjbGVhckNhbGN1bGF0aW9uIiwiY2xlYXJFcnJvciIsImdldEZvcm1hdHRlZENhbGN1bGF0aW9uIiwiaXNDb25uZWN0ZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSignaturePurchase.ts\n"));

/***/ })

});