# USDT 购买功能修复总结

## 🎯 问题描述

在 demo 中，TON 购买功能正常工作，但 USDT 购买功能失败。原因是 `useSignaturePurchase` hook 对所有货币类型都使用相同的直接合约调用方式，而 USDT 购买需要通过 jetton transfer 的方式进行。

## ✅ 修复内容

### 1. 安装依赖
- 添加了 `tonweb` 库到 package.json
- 添加了 `axios` 库（如果尚未安装）
- 创建了 TonWeb 的 TypeScript 类型声明文件

### 2. 修复 useSignaturePurchase Hook

#### 主要变更：
- **分离购买逻辑**：将 `executePurchase` 函数拆分为 `executeTONPurchase` 和 `executeUSDTPurchase`
- **添加 jetton wallet 地址计算**：实现了 `getJettonWalletAddress` 函数
- **正确的 USDT 购买流程**：使用 jetton transfer 而不是直接合约调用

#### 新增功能：
```typescript
// 自动检测货币类型并选择正确的购买方式
if (calc.currency === 0) {
  // TON 购买 - 直接合约调用
  return await executeTONPurchase(calc, signature)
} else if (calc.currency === 1) {
  // USDT 购买 - jetton transfer
  return await executeUSDTPurchase(calc, signature)
}
```

### 3. USDT 购买流程

#### 步骤：
1. **计算 jetton wallet 地址**：使用 TonWeb 计算用户的 USDT jetton wallet 地址
2. **构建 forward_payload**：包含签名验证数据的 payload
3. **创建 jetton transfer 消息**：发送到用户的 USDT wallet
4. **自动转发到合约**：jetton wallet 会自动转发到拍卖合约

#### 消息结构：
```typescript
const jettonTransferBody = beginCell()
  .storeUint(0xf8a7ea5, 32) // JettonTransfer op code
  .storeUint(0, 64) // query_id
  .storeCoins(amount) // USDT amount
  .storeAddress(auctionContract) // destination
  .storeAddress(userAddress) // response_destination
  .storeMaybeRef(null) // custom_payload
  .storeCoins(toNano('0.05')) // forward_ton_amount
  .storeMaybeRef(forwardPayload) // signature data
  .endCell()
```

## 🔧 配置

### 环境变量
确保设置了正确的合约地址：
```env
NEXT_PUBLIC_AUCTION_CONTRACT_ADDRESS=EQC... # 拍卖合约地址
```

### USDT Jetton Master 地址
代码中已配置测试网 USDT jetton master 地址：
```typescript
const USDT_JETTON_MASTER_ADDRESS = 'kQAzft3exsq946eO92eOF0QkQqNFOLaPHak18Xdy4OYG9WjN'
```

## 🧪 测试

### 运行测试
```bash
cd onion-launch/frontend
npx ts-node src/test/runTests.ts
```

### 测试内容
1. **Forward Payload 构建测试**：验证签名数据正确打包
2. **Jetton Transfer 消息测试**：验证消息结构正确
3. **计算一致性测试**：验证 USDT 到代币的转换计算
4. **签名结构测试**：验证签名数据格式

## 🚀 使用方式

### 前端使用
```typescript
import { useSignaturePurchase } from '@/hooks/useSignaturePurchase'

function PurchaseComponent() {
  const { calculatePurchase, executePurchase } = useSignaturePurchase()
  
  // 计算购买（支持 TON 和 USDT）
  const calculation = await calculatePurchase({
    amount: '25', // 25 USDT
    currency: 'USDT'
  })
  
  // 执行购买（自动选择正确的流程）
  const success = await executePurchase()
}
```

### 自动流程选择
- **TON 购买**：直接发送 `PurchaseWithSignature` 消息到拍卖合约
- **USDT 购买**：发送 `JettonTransfer` 消息到用户的 USDT wallet，包含签名验证数据

## 🔍 关键改进

### 1. 类型安全
- 添加了完整的 TypeScript 类型定义
- 确保 PurchaseCalculation 类型一致性

### 2. 错误处理
- 改进了错误消息和用户反馈
- 添加了详细的调试日志

### 3. Gas 优化
- TON 购买：0.5 TON gas
- USDT 购买：0.1 TON gas + 0.05 TON forward amount

### 4. 安全性
- 保持了签名验证机制
- 防重放攻击（nonce）
- 用户地址验证

## 📝 注意事项

1. **测试网环境**：当前配置使用测试网 USDT jetton
2. **Gas 费用**：USDT 购买需要额外的 gas 用于 jetton transfer
3. **钱包余额**：用户需要有足够的 USDT 和 TON（用于 gas）
4. **合约配置**：确保拍卖合约已正确配置 USDT 支持

## 🎉 结果

修复后，用户现在可以：
- ✅ 使用 TON 购买代币（原有功能）
- ✅ 使用 USDT 购买代币（新修复功能）
- ✅ 自动选择正确的购买流程
- ✅ 享受完整的签名验证安全性

USDT 购买流程现在应该能够正常工作，与 TON 购买一样稳定可靠。
