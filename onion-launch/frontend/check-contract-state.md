# Contract State Debugging Guide

## 检查合约状态的步骤

### 1. 验证合约地址
确保 `.env.local` 中的合约地址是正确的：
```
NEXT_PUBLIC_AUCTION_CONTRACT_ADDRESS=kQCqsh_qjhmyG7dtg3cTGWB1IxUceqXqD06mM-mqaI_SRTAJ
```

### 2. 检查合约是否已部署
在 TON Explorer 中查看合约：
https://testnet.tonviewer.com/kQCqsh_qjhmyG7dtg3cTGWB1IxUceqXqD06mM-mqaI_SRTAJ

### 3. 检查合约状态
需要验证以下状态：

#### 3.1 拍卖状态 (auction_status)
- 0 = pending (未开始)
- 1 = active (进行中) ← 需要是这个状态
- 2 = ended_success (成功结束)
- 3 = ended_failure (失败结束)

#### 3.2 签名公钥 (signing_public_key)
- 必须不为 0
- 必须与服务器的公钥匹配

#### 3.3 拍卖时间
- start_time: 必须 <= 当前时间
- end_time: 必须 > 当前时间

#### 3.4 最小购买金额 (min_purchase)
- 必须 > 0
- 用户购买金额必须 >= 这个值

### 4. 常见错误代码
根据我们添加的错误代码：

- 1001: ERROR_AUCTION_NOT_ACTIVE - 拍卖未激活
- 1002: ERROR_AUCTION_NOT_STARTED - 拍卖未开始
- 1003: ERROR_AUCTION_ENDED - 拍卖已结束
- 1009: ERROR_SIGNING_KEY_NOT_SET - 签名密钥未设置
- 1010: ERROR_SIGNATURE_EXPIRED - 签名已过期
- 1013: ERROR_INVALID_SIGNATURE - 签名无效

### 5. 调试步骤

1. **检查合约初始化**：
   - 合约是否正确部署？
   - 拍卖是否已启动？
   - 签名密钥是否已设置？

2. **检查消息格式**：
   - Op code 是否正确 (1524770820 = 0x5ae22804)？
   - PurchaseCalculation 结构是否正确？
   - 签名是否正确存储为引用？

3. **检查签名验证**：
   - 服务器签名是否有效？
   - 时间戳是否在有效范围内？
   - Nonce 是否已被使用？

### 6. 修复建议

如果交易失败，按以下顺序检查：

1. **合约状态问题**：
   ```typescript
   // 调用合约的 getter 方法检查状态
   const auctionStatus = await contract.getAuctionStatus();
   const signingKey = await contract.getSigningPublicKey();
   ```

2. **消息结构问题**：
   - 确保使用正确的 op code
   - 确保 PurchaseCalculation 字段顺序正确
   - 确保签名作为引用存储

3. **签名问题**：
   - 检查服务器签名逻辑
   - 确保时间戳在 5 分钟内
   - 确保 nonce 未被重复使用
