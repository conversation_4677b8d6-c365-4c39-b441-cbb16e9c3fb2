# Transaction Signing Implementation

## Overview

This document explains how the "Sign Transaction" functionality works in the ONION token purchase flow.

## Flow Description

### 1. Purchase Calculation
When a user enters a purchase amount and clicks "Calculate", the frontend:
- Calls the backend API to calculate the purchase details
- Receives a signed calculation with server signature
- Displays the calculation results to the user

### 2. Purchase Confirmation
When the user clicks "Purchase ONION Tokens":
- A confirmation modal opens showing all purchase details
- User must first click "Confirm Details" to acknowledge the transaction
- Button text changes to "Sign Transaction"

### 3. Transaction Signing
When the user clicks "Sign Transaction":
- The `executePurchase()` function in `useSignaturePurchase` hook is called
- A `PurchaseWithSignature` message is constructed with:
  - Op code: `1524770820`
  - PurchaseCalculation struct (user, amount, currency, tokens, price, round, timestamp, nonce)
  - Server signature (stored as reference cell)
- Transaction is sent to the smart contract using TON Connect UI
- User's wallet prompts them to sign the transaction

## Technical Implementation

### Message Structure
```typescript
const purchaseBody = beginCell()
  .storeUint(1524770820, 32) // PurchaseWithSignature op code
  // Store PurchaseCalculation struct
  .storeAddress(Address.parse(calc.user))
  .storeCoins(BigInt(calc.amount))
  .storeUint(calc.currency, 8)
  .storeCoins(BigInt(calc.tokens_to_receive))
  .storeCoins(BigInt(calc.current_price))
  .storeUint(calc.current_round, 32)
  .storeUint(calc.timestamp, 64)
  .storeUint(BigInt(calc.nonce), 64)
  // Store signature as reference
  .storeRef(beginCell().storeBuffer(signatureBuffer).endCell())
  .endCell()
```

### Transaction Request
```typescript
const transactionRequest = {
  validUntil: Math.floor(Date.now() / 1000) + 600, // 10 minutes validity
  messages: [
    {
      address: CONTRACT_ADDRESS,
      amount: toNano('0.5').toString(), // Gas fee
      payload: Buffer.from(purchaseBody.toBoc()).toString('base64')
    }
  ]
}

const result = await tonConnectUI.sendTransaction(transactionRequest)
```

## Configuration

### Environment Variables
Add to your `.env.local` file:
```
NEXT_PUBLIC_AUCTION_CONTRACT_ADDRESS=EQC...your-contract-address
```

### Dependencies
- `@tonconnect/ui-react` - TON Connect UI integration
- `@ton/core` - TON blockchain utilities
- `@ton/ton` - TON client library

## Error Handling

The implementation includes specific error handling for:
- User rejection: "Transaction was cancelled by user"
- Insufficient funds: "Insufficient funds for transaction"
- Network errors: Displays the actual error message
- Generic errors: Falls back to API error handling

## Security Features

1. **Signature Verification**: All purchases require server-signed calculations
2. **Nonce Protection**: Prevents replay attacks
3. **Timestamp Validation**: Signatures expire after 5 minutes
4. **Amount Verification**: Smart contract verifies calculation matches signature

## User Experience

1. **Two-Step Confirmation**: Users must confirm details before signing
2. **Clear Button States**: Button text changes from "Confirm Details" to "Sign Transaction"
3. **Loading States**: Shows spinner during transaction processing
4. **Error Feedback**: Clear error messages for different failure scenarios
