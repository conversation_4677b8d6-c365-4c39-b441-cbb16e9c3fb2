"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSignaturePurchase.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSignaturePurchase.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSignaturePurchase: () => (/* binding */ useSignaturePurchase)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/./node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/apiService */ \"(app-pages-browser)/./src/lib/apiService.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/../node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useSignaturePurchase auto */ \n\n\n\n// Contract configuration - in production this should come from environment variables\nconst CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0 // Replace with actual contract address\n;\nconsole.log('Using auction contract address:', CONTRACT_ADDRESS);\nfunction useSignaturePurchase() {\n    var _wallet_account;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isCalculating: false,\n        isProcessing: false,\n        calculation: null,\n        error: null\n    });\n    /**\n   * Calculate purchase with signature verification\n   * This is the only supported purchase method - all purchases must be signature verified\n   */ const calculatePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[calculatePurchase]\": async (params)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'Wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                        ...prev,\n                        isCalculating: true,\n                        error: null,\n                        calculation: null\n                    })\n            }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n            try {\n                // Convert amount to nanotons/smallest unit\n                const amountBigInt = BigInt(Math.floor(parseFloat(params.amount) * **********));\n                const currency = params.currency === 'TON' ? 0 : 1;\n                const response = await _lib_apiService__WEBPACK_IMPORTED_MODULE_3__.ApiService.calculatePurchase({\n                    user_address: wallet.account.address,\n                    amount: amountBigInt.toString(),\n                    currency\n                });\n                if (!response.success) {\n                    throw new Error(response.error || 'Calculation failed');\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            calculation: response\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return response;\n            } catch (error) {\n                const errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[calculatePurchase]\"], [\n        wallet\n    ]);\n    /**\n   * Execute purchase with signature verification\n   * All purchases must go through this signature verification process\n   */ const executePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executePurchase]\": async ()=>{\n            if (!wallet || !state.calculation || !tonConnectUI) {\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'No calculation available or wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: true,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n            try {\n                const calc = state.calculation.calculation;\n                const signature = state.calculation.signature;\n                console.log('Executing purchase with signature:', {\n                    calculation: calc,\n                    signature: signature\n                });\n                console.log('Contract address:', CONTRACT_ADDRESS);\n                console.log('Calculation details:', {\n                    user: calc.user,\n                    amount: calc.amount,\n                    currency: calc.currency,\n                    tokens_to_receive: calc.tokens_to_receive,\n                    current_price: calc.current_price,\n                    current_round: calc.current_round,\n                    timestamp: calc.timestamp,\n                    nonce: calc.nonce\n                });\n                // Convert signature from base64 to buffer\n                const signatureBuffer = Buffer.from(signature, 'base64');\n                // Create signature cell\n                const signatureCell = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeBuffer(signatureBuffer).endCell();\n                // Create the PurchaseWithSignature message body following the exact generated structure\n                const purchaseBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(1524770820, 32) // PurchaseWithSignature op code\n                // Store PurchaseCalculation inline (not as a separate cell)\n                .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user)).storeCoins(BigInt(calc.amount)).storeUint(calc.currency, 8).storeCoins(BigInt(calc.tokens_to_receive)).storeCoins(BigInt(calc.current_price)).storeUint(calc.current_round, 32).storeUint(calc.timestamp, 64).storeUint(BigInt(calc.nonce), 64)// Store signature as reference\n                .storeRef(signatureCell).endCell();\n                console.log('Message structure:', {\n                    opCode: '0x' + 1524770820..toString(16),\n                    signatureCellBits: signatureCell.bits.length,\n                    totalBodyBits: purchaseBody.bits.length,\n                    bodyBocLength: purchaseBody.toBoc().length\n                });\n                // Create transaction request\n                const transactionRequest = {\n                    validUntil: Math.floor(Date.now() / 1000) + 600,\n                    messages: [\n                        {\n                            address: CONTRACT_ADDRESS,\n                            amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                            payload: Buffer.from(purchaseBody.toBoc()).toString('base64')\n                        }\n                    ]\n                };\n                console.log('Transaction request:', {\n                    contractAddress: CONTRACT_ADDRESS,\n                    gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                    validUntil: transactionRequest.validUntil,\n                    payloadLength: transactionRequest.messages[0].payload.length\n                });\n                // Send transaction using TON Connect UI\n                const result = await tonConnectUI.sendTransaction(transactionRequest);\n                console.log('Transaction sent:', result);\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            isProcessing: false,\n                            calculation: null // Clear after successful purchase\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return true;\n            } catch (error) {\n                console.error('Transaction failed:', error);\n                let errorMessage = 'Transaction failed';\n                if (error instanceof Error) {\n                    if (error.message.includes('User rejected')) {\n                        errorMessage = 'Transaction was cancelled by user';\n                    } else if (error.message.includes('Insufficient funds')) {\n                        errorMessage = 'Insufficient funds for transaction';\n                    } else {\n                        errorMessage = error.message;\n                    }\n                } else {\n                    errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            isProcessing: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[executePurchase]\"], [\n        wallet,\n        state.calculation,\n        tonConnectUI\n    ]);\n    /**\n   * Clear current calculation\n   */ const clearCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearCalculation]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearCalculation]\": (prev)=>({\n                        ...prev,\n                        calculation: null,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearCalculation]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearCalculation]\"], []);\n    /**\n   * Clear error\n   */ const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearError]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearError]\": (prev)=>({\n                        ...prev,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearError]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearError]\"], []);\n    /**\n   * Get formatted calculation data for display\n   */ const getFormattedCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[getFormattedCalculation]\": ()=>{\n            if (!state.calculation) return null;\n            const calc = state.calculation.calculation;\n            return {\n                amount: parseFloat(calc.amount) / **********,\n                currency: calc.currency === 0 ? 'TON' : 'USDT',\n                tokensToReceive: parseFloat(calc.tokens_to_receive) / **********,\n                currentPrice: parseFloat(calc.current_price) / **********,\n                currentRound: calc.current_round,\n                timestamp: new Date(calc.timestamp * 1000),\n                nonce: calc.nonce\n            };\n        }\n    }[\"useSignaturePurchase.useCallback[getFormattedCalculation]\"], [\n        state.calculation\n    ]);\n    return {\n        ...state,\n        calculatePurchase,\n        executePurchase,\n        clearCalculation,\n        clearError,\n        getFormattedCalculation,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSignaturePurchase.ts\n"));

/***/ })

});