// Test script to verify message structure
const { beginCell, Address } = require('@ton/core');

// Test data (similar to what we get from the API)
const testCalculation = {
  user: "kQBH9JBIcPSCALy9NYKe6sS6xWvTXGxcyD22TfvXQLUqv8_5", // Example user address
  amount: "100000000000", // 100 TON in nanotons
  currency: 0, // TON
  tokens_to_receive: "1000000000000", // 1000 tokens
  current_price: "100000000", // 0.1 TON per token
  current_round: 1,
  timestamp: Math.floor(Date.now() / 1000),
  nonce: "12345"
};

const testSignature = "dGVzdF9zaWduYXR1cmVfZGF0YQ=="; // Base64 test signature

console.log('Test calculation:', testCalculation);

try {
  // Convert signature from base64 to buffer
  const signatureBuffer = Buffer.from(testSignature, 'base64');
  
  // Create PurchaseCalculation cell
  const calculationCell = beginCell()
    .storeAddress(Address.parse(testCalculation.user))
    .storeCoins(BigInt(testCalculation.amount))
    .storeUint(testCalculation.currency, 8)
    .storeCoins(BigInt(testCalculation.tokens_to_receive))
    .storeCoins(BigInt(testCalculation.current_price))
    .storeUint(testCalculation.current_round, 32)
    .storeUint(testCalculation.timestamp, 64)
    .storeUint(BigInt(testCalculation.nonce), 64)
    .endCell();
  
  // Create signature cell
  const signatureCell = beginCell()
    .storeBuffer(signatureBuffer)
    .endCell();
  
  // Create the PurchaseWithSignature message body
  const purchaseBody = beginCell()
    .storeUint(1524770820, 32) // PurchaseWithSignature op code
    .storeSlice(calculationCell.beginParse()) // Store calculation inline
    .storeRef(signatureCell) // Store signature as reference
    .endCell();
    
  console.log('Message structure created successfully:');
  console.log('- Op code: 0x' + (1524770820).toString(16));
  console.log('- Calculation cell bits:', calculationCell.bits.length);
  console.log('- Signature cell bits:', signatureCell.bits.length);
  console.log('- Total body bits:', purchaseBody.bits.length);
  console.log('- Body BOC length:', purchaseBody.toBoc().length);
  
  // Test parsing back
  const bodySlice = purchaseBody.beginParse();
  const opCode = bodySlice.loadUint(32);
  console.log('Parsed op code:', opCode, '(0x' + opCode.toString(16) + ')');
  
  if (opCode === 1524770820) {
    console.log('✅ Op code matches!');
  } else {
    console.log('❌ Op code mismatch!');
  }
  
} catch (error) {
  console.error('Error creating message structure:', error);
}
