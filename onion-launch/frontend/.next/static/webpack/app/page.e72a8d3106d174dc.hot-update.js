"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useSignaturePurchase.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useSignaturePurchase.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSignaturePurchase: () => (/* binding */ useSignaturePurchase)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/./node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/apiService */ \"(app-pages-browser)/./src/lib/apiService.ts\");\n/* harmony import */ var _lib_usdtPurchase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/usdtPurchase */ \"(app-pages-browser)/./src/lib/usdtPurchase.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/../node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useSignaturePurchase auto */ \n\n\n\n\n// Contract configuration - in production this should come from environment variables\nconst CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0 // Replace with actual contract address\n;\nconst USDT_JETTON_MASTER_ADDRESS = 'kQAzft3exsq946eO92eOF0QkQqNFOLaPHak18Xdy4OYG9WjN' // Testnet USDT jetton master\n;\nconsole.log('Using auction contract address:', CONTRACT_ADDRESS);\nconsole.log('Using USDT jetton master address:', USDT_JETTON_MASTER_ADDRESS);\n// Helper function to calculate jetton wallet address\nasync function getJettonWalletAddress(ownerAddress, jettonMasterAddress) {\n    try {\n        // Use TonWeb to calculate jetton wallet address\n        const TonWeb = (await Promise.resolve().then(function webpackMissingModule() { var e = new Error(\"Cannot find module 'tonweb'\"); e.code = 'MODULE_NOT_FOUND'; throw e; })).default;\n        const tonweb = new TonWeb(new TonWeb.HttpProvider('https://testnet.toncenter.com/api/v2/jsonRPC'));\n        //@ts-ignore\n        const jettonMasterContract = new TonWeb.token.jetton.JettonMinter(tonweb.provider, {\n            address: jettonMasterAddress\n        });\n        const jettonWalletAddress = await jettonMasterContract.getJettonWalletAddress(new TonWeb.utils.Address(ownerAddress));\n        return jettonWalletAddress.toString();\n    } catch (error) {\n        console.error('Failed to get jetton wallet address:', error);\n        throw new Error('Failed to calculate USDT wallet address');\n    }\n}\nfunction useSignaturePurchase() {\n    var _wallet_account;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isCalculating: false,\n        isProcessing: false,\n        calculation: null,\n        error: null\n    });\n    /**\n   * Calculate purchase with signature verification\n   * This is the only supported purchase method - all purchases must be signature verified\n   */ const calculatePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[calculatePurchase]\": async (params)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'Wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                        ...prev,\n                        isCalculating: true,\n                        error: null,\n                        calculation: null\n                    })\n            }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n            try {\n                // Convert amount to nanotons/smallest unit\n                const amountBigInt = BigInt(Math.floor(parseFloat(params.amount) * **********));\n                const currency = params.currency === 'TON' ? 0 : 1;\n                const response = await _lib_apiService__WEBPACK_IMPORTED_MODULE_3__.ApiService.calculatePurchase({\n                    user_address: wallet.account.address,\n                    amount: amountBigInt.toString(),\n                    currency\n                });\n                if (!response.success) {\n                    throw new Error(response.error || 'Calculation failed');\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            calculation: response\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return response;\n            } catch (error) {\n                const errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                setState({\n                    \"useSignaturePurchase.useCallback[calculatePurchase]\": (prev)=>({\n                            ...prev,\n                            isCalculating: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[calculatePurchase]\"]);\n                return null;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[calculatePurchase]\"], [\n        wallet\n    ]);\n    /**\n   * Execute purchase with signature verification\n   * All purchases must go through this signature verification process\n   */ const executePurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executePurchase]\": async ()=>{\n            if (!wallet || !state.calculation || !tonConnectUI) {\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            error: 'No calculation available or wallet not connected'\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n            setState({\n                \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: true,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n            try {\n                const calc = state.calculation.calculation;\n                const signature = state.calculation.signature;\n                console.log('Executing purchase with signature:', {\n                    calculation: calc,\n                    signature: signature\n                });\n                console.log('Contract address:', CONTRACT_ADDRESS);\n                console.log('Calculation details:', {\n                    user: calc.user,\n                    amount: calc.amount,\n                    currency: calc.currency,\n                    tokens_to_receive: calc.tokens_to_receive,\n                    current_price: calc.current_price,\n                    current_round: calc.current_round,\n                    timestamp: calc.timestamp,\n                    nonce: calc.nonce\n                });\n                // Handle different currencies\n                if (calc.currency === 0) {\n                    // TON purchase - direct contract call\n                    return await executeTONPurchase(calc, signature);\n                } else if (calc.currency === 1) {\n                    // USDT purchase - jetton transfer\n                    return await executeUSDTPurchase(calc, signature);\n                } else {\n                    throw new Error('Unsupported currency');\n                }\n            } catch (error) {\n                console.error('Transaction failed:', error);\n                let errorMessage = 'Transaction failed';\n                if (error instanceof Error) {\n                    if (error.message.includes('User rejected')) {\n                        errorMessage = 'Transaction was cancelled by user';\n                    } else if (error.message.includes('Insufficient funds')) {\n                        errorMessage = 'Insufficient funds for transaction';\n                    } else {\n                        errorMessage = error.message;\n                    }\n                } else {\n                    errorMessage = (0,_lib_apiService__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n                }\n                setState({\n                    \"useSignaturePurchase.useCallback[executePurchase]\": (prev)=>({\n                            ...prev,\n                            isProcessing: false,\n                            error: errorMessage\n                        })\n                }[\"useSignaturePurchase.useCallback[executePurchase]\"]);\n                return false;\n            }\n        }\n    }[\"useSignaturePurchase.useCallback[executePurchase]\"], [\n        wallet,\n        state.calculation,\n        tonConnectUI\n    ]);\n    /**\n   * Execute TON purchase with signature verification\n   */ const executeTONPurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executeTONPurchase]\": async (calc, signature)=>{\n            // Convert signature from base64 to buffer\n            const signatureBuffer = Buffer.from(signature, 'base64');\n            // Create signature cell\n            const signatureCell = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeBuffer(signatureBuffer).endCell();\n            // Create the PurchaseWithSignature message body following the exact generated structure\n            const purchaseBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(1524770820, 32) // PurchaseWithSignature op code\n            // Store PurchaseCalculation inline (not as a separate cell)\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user)).storeCoins(BigInt(calc.amount)).storeUint(calc.currency, 8).storeCoins(BigInt(calc.tokens_to_receive)).storeCoins(BigInt(calc.current_price)).storeUint(calc.current_round, 32).storeUint(calc.timestamp, 64).storeUint(BigInt(calc.nonce), 64)// Store signature as reference\n            .storeRef(signatureCell).endCell();\n            console.log('Message structure:', {\n                opCode: '0x' + 1524770820..toString(16),\n                signatureCellBits: signatureCell.bits.length,\n                totalBodyBits: purchaseBody.bits.length,\n                bodyBocLength: purchaseBody.toBoc().length\n            });\n            // Create transaction request\n            const transactionRequest = {\n                validUntil: Math.floor(Date.now() / 1000) + 600,\n                messages: [\n                    {\n                        address: CONTRACT_ADDRESS,\n                        amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                        payload: Buffer.from(purchaseBody.toBoc()).toString('base64')\n                    }\n                ]\n            };\n            console.log('Transaction request:', {\n                contractAddress: CONTRACT_ADDRESS,\n                gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.5').toString(),\n                validUntil: transactionRequest.validUntil,\n                payloadLength: transactionRequest.messages[0].payload.length\n            });\n            // Send transaction using TON Connect UI\n            const result = await tonConnectUI.sendTransaction(transactionRequest);\n            console.log('TON Transaction sent:', result);\n            setState({\n                \"useSignaturePurchase.useCallback[executeTONPurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: false,\n                        calculation: null // Clear after successful purchase\n                    })\n            }[\"useSignaturePurchase.useCallback[executeTONPurchase]\"]);\n            return true;\n        }\n    }[\"useSignaturePurchase.useCallback[executeTONPurchase]\"], [\n        tonConnectUI\n    ]);\n    /**\n   * Execute USDT purchase with signature verification\n   */ const executeUSDTPurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[executeUSDTPurchase]\": async (calc, signature)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                throw new Error('Wallet not connected');\n            }\n            // Get user's USDT jetton wallet address\n            const userUSDTWalletAddress = await getJettonWalletAddress(wallet.account.address, USDT_JETTON_MASTER_ADDRESS);\n            console.log('User USDT wallet address:', userUSDTWalletAddress);\n            // Build forward payload with signature verification data\n            const forwardPayload = (0,_lib_usdtPurchase__WEBPACK_IMPORTED_MODULE_4__.buildUSDTForwardPayload)({\n                user: _ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(calc.user),\n                amount: BigInt(calc.amount),\n                currency: calc.currency,\n                tokensToReceive: BigInt(calc.tokens_to_receive),\n                currentPrice: BigInt(calc.current_price),\n                currentRound: calc.current_round,\n                timestamp: calc.timestamp,\n                nonce: BigInt(calc.nonce)\n            }, signature);\n            // Create jetton transfer message\n            const jettonTransferBody = (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(0xf8a7ea5, 32) // JettonTransfer op code\n            .storeUint(0, 64) // query_id\n            .storeCoins(BigInt(calc.amount)) // amount\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(CONTRACT_ADDRESS)) // destination (auction contract)\n            .storeAddress(_ton_core__WEBPACK_IMPORTED_MODULE_2__.Address.parse(wallet.account.address)) // response_destination\n            .storeMaybeRef(null) // custom_payload\n            .storeCoins((0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.05')) // forward_ton_amount (gas for signature verification)\n            .storeMaybeRef(forwardPayload) // forward_payload with signature data\n            .endCell();\n            console.log('USDT Transfer details:', {\n                userWallet: userUSDTWalletAddress,\n                amount: calc.amount,\n                destination: CONTRACT_ADDRESS,\n                forwardPayloadSize: forwardPayload.toBoc().length\n            });\n            // Create transaction request for USDT transfer\n            const transactionRequest = {\n                validUntil: Math.floor(Date.now() / 1000) + 600,\n                messages: [\n                    {\n                        address: userUSDTWalletAddress,\n                        amount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.1').toString(),\n                        payload: Buffer.from(jettonTransferBody.toBoc()).toString('base64')\n                    }\n                ]\n            };\n            console.log('USDT Transaction request:', {\n                jettonWalletAddress: userUSDTWalletAddress,\n                gasAmount: (0,_ton_core__WEBPACK_IMPORTED_MODULE_2__.toNano)('0.1').toString(),\n                validUntil: transactionRequest.validUntil,\n                payloadLength: transactionRequest.messages[0].payload.length\n            });\n            // Send transaction using TON Connect UI\n            const result = await tonConnectUI.sendTransaction(transactionRequest);\n            console.log('USDT Transaction sent:', result);\n            setState({\n                \"useSignaturePurchase.useCallback[executeUSDTPurchase]\": (prev)=>({\n                        ...prev,\n                        isProcessing: false,\n                        calculation: null // Clear after successful purchase\n                    })\n            }[\"useSignaturePurchase.useCallback[executeUSDTPurchase]\"]);\n            return true;\n        }\n    }[\"useSignaturePurchase.useCallback[executeUSDTPurchase]\"], [\n        wallet,\n        tonConnectUI\n    ]);\n    /**\n   * Clear current calculation\n   */ const clearCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearCalculation]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearCalculation]\": (prev)=>({\n                        ...prev,\n                        calculation: null,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearCalculation]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearCalculation]\"], []);\n    /**\n   * Clear error\n   */ const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[clearError]\": ()=>{\n            setState({\n                \"useSignaturePurchase.useCallback[clearError]\": (prev)=>({\n                        ...prev,\n                        error: null\n                    })\n            }[\"useSignaturePurchase.useCallback[clearError]\"]);\n        }\n    }[\"useSignaturePurchase.useCallback[clearError]\"], []);\n    /**\n   * Get formatted calculation data for display\n   */ const getFormattedCalculation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSignaturePurchase.useCallback[getFormattedCalculation]\": ()=>{\n            if (!state.calculation) return null;\n            const calc = state.calculation.calculation;\n            return {\n                amount: parseFloat(calc.amount) / **********,\n                currency: calc.currency === 0 ? 'TON' : 'USDT',\n                tokensToReceive: parseFloat(calc.tokens_to_receive) / **********,\n                currentPrice: parseFloat(calc.current_price) / **********,\n                currentRound: calc.current_round,\n                timestamp: new Date(calc.timestamp * 1000),\n                nonce: calc.nonce\n            };\n        }\n    }[\"useSignaturePurchase.useCallback[getFormattedCalculation]\"], [\n        state.calculation\n    ]);\n    return {\n        ...state,\n        calculatePurchase,\n        executePurchase,\n        clearCalculation,\n        clearError,\n        getFormattedCalculation,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSignaturePurchase.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/usdtPurchase.ts":
/*!*********************************!*\
  !*** ./src/lib/usdtPurchase.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   USDTPurchaseService: () => (/* binding */ USDTPurchaseService),\n/* harmony export */   buildUSDTForwardPayload: () => (/* binding */ buildUSDTForwardPayload),\n/* harmony export */   createPurchaseCalculation: () => (/* binding */ createPurchaseCalculation),\n/* harmony export */   createUSDTPurchaseWithSignature: () => (/* binding */ createUSDTPurchaseWithSignature),\n/* harmony export */   generateNonce: () => (/* binding */ generateNonce)\n/* harmony export */ });\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/./node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_0__);\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/../node_modules/buffer/index.js\")[\"Buffer\"];\n\n// Build forward_payload for USDT purchase with signature verification\nfunction buildUSDTForwardPayload(calculation, signature) {\n    // Convert base64 signature to Slice\n    const signatureBuffer = Buffer.from(signature, 'base64');\n    const signatureCell = (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeBuffer(signatureBuffer).endCell();\n    // Build the forward payload\n    return (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeAddress(calculation.user).storeCoins(calculation.amount).storeUint(calculation.currency, 8).storeCoins(calculation.tokensToReceive).storeCoins(calculation.currentPrice).storeUint(calculation.currentRound, 32).storeUint(calculation.timestamp, 64).storeUint(Number(calculation.nonce), 64).storeRef(signatureCell).endCell();\n}\n// Generate a random nonce for anti-replay protection\nfunction generateNonce() {\n    return BigInt(Math.floor(Math.random() * **********000));\n}\n// Create purchase calculation object\nfunction createPurchaseCalculation(user, usdtAmount, tokensToReceive, currentPrice, currentRound) {\n    return {\n        user,\n        amount: usdtAmount,\n        currency: 1,\n        tokensToReceive,\n        currentPrice,\n        currentRound,\n        timestamp: Math.floor(Date.now() / 1000),\n        nonce: generateNonce()\n    };\n}\n// API service for getting server signatures\nclass USDTPurchaseService {\n    // Get signature from server for purchase calculation\n    async getSignature(calculation) {\n        const response = await fetch(\"\".concat(this.apiBaseUrl, \"/api/purchase/sign\"), {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                user: calculation.user.toString(),\n                amount: calculation.amount.toString(),\n                currency: calculation.currency,\n                tokensToReceive: calculation.tokensToReceive.toString(),\n                currentPrice: calculation.currentPrice.toString(),\n                currentRound: calculation.currentRound,\n                timestamp: calculation.timestamp,\n                nonce: calculation.nonce.toString()\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to get signature: \".concat(response.statusText));\n        }\n        const data = await response.json();\n        return data.signature;\n    }\n    // Get current auction state for calculation\n    async getAuctionState() {\n        const response = await fetch(\"\".concat(this.apiBaseUrl, \"/api/auction/state\"));\n        if (!response.ok) {\n            throw new Error(\"Failed to get auction state: \".concat(response.statusText));\n        }\n        const data = await response.json();\n        return {\n            currentPrice: BigInt(data.currentPrice),\n            currentRound: data.currentRound,\n            tokensAvailable: BigInt(data.tokensAvailable)\n        };\n    }\n    // Calculate tokens to receive for USDT amount\n    calculateTokensToReceive(usdtAmount, currentPrice) {\n        // Convert USDT (6 decimals) to TON equivalent (9 decimals)\n        const usdtInTonUnits = usdtAmount * 1000n;\n        // Calculate tokens using the same formula as the contract\n        return usdtInTonUnits * **********n / currentPrice;\n    }\n    constructor(apiBaseUrl){\n        this.apiBaseUrl = apiBaseUrl;\n    }\n}\n// Example usage helper\nasync function createUSDTPurchaseWithSignature(user, usdtAmount, apiService) {\n    // Get current auction state\n    const auctionState = await apiService.getAuctionState();\n    // Calculate tokens to receive\n    const tokensToReceive = apiService.calculateTokensToReceive(usdtAmount, auctionState.currentPrice);\n    // Create purchase calculation\n    const calculation = createPurchaseCalculation(user, usdtAmount, tokensToReceive, auctionState.currentPrice, auctionState.currentRound);\n    // Get signature from server\n    const signature = await apiService.getSignature(calculation);\n    // Build forward payload\n    const forwardPayload = buildUSDTForwardPayload(calculation, signature);\n    return {\n        calculation,\n        signature,\n        forwardPayload\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/usdtPurchase.ts\n"));

/***/ })

});