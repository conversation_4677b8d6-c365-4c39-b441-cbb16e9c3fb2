declare module 'tonweb' {
  export default class TonWeb {
    constructor(provider: any)
    provider: any
    static utils: {
      Address: new (address: string) => any
    }
    static token: {
      jetton: {
        JettonMinter: new (provider: any, options: { address: string }) => {
          getJettonWalletAddress(ownerAddress: any): Promise<any>
        }
      }
    }
    static HttpProvider: new (endpoint: string) => any
  }
}
