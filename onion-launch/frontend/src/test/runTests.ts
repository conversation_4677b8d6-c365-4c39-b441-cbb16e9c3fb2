#!/usr/bin/env node

/**
 * Test runner for USDT purchase functionality
 * Run with: npx ts-node src/test/runTests.ts
 */

import { runAllTests } from './usdtPurchaseTest'

async function main() {
  console.log('🔧 USDT Purchase Implementation Test Suite')
  console.log('==========================================\n')
  
  try {
    const success = runAllTests()
    
    if (success) {
      console.log('\n✅ All tests passed successfully!')
      console.log('The USDT purchase implementation is ready for testing.')
      process.exit(0)
    } else {
      console.log('\n❌ Some tests failed.')
      console.log('Please review the implementation before proceeding.')
      process.exit(1)
    }
  } catch (error) {
    console.error('\n💥 Test suite crashed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
